#!/usr/bin/env python3
"""
Extract Z3X Hardware ID - Extrae información específica de hardware que Z3X usa para identificación
"""

import os
import sys
import json
import winreg
import subprocess
import platform
from datetime import datetime

def get_machine_guid():
    """Obtiene el Machine GUID de Windows"""
    try:
        with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Microsoft\Cryptography") as key:
            machine_guid, _ = winreg.QueryValueEx(key, "MachineGuid")
            return machine_guid
    except Exception as e:
        return f"Error: {e}"

def get_cpu_info():
    """Obtiene información del CPU"""
    try:
        with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, r"HARDWARE\DESCRIPTION\System\CentralProcessor\0") as key:
            cpu_name, _ = winreg.QueryValueEx(key, "ProcessorNameString")
            cpu_id, _ = winreg.QueryValueEx(key, "Identifier")
            return {
                'name': cpu_name,
                'identifier': cpu_id
            }
    except Exception as e:
        return f"Error: {e}"

def get_bios_info():
    """Obtiene información del BIOS"""
    try:
        with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, r"HARDWARE\DESCRIPTION\System\BIOS") as key:
            bios_vendor, _ = winreg.QueryValueEx(key, "BIOSVendor")
            bios_version, _ = winreg.QueryValueEx(key, "BIOSVersion")
            return {
                'vendor': bios_vendor,
                'version': bios_version
            }
    except Exception as e:
        return f"Error: {e}"

def get_mac_addresses():
    """Obtiene direcciones MAC de adaptadores de red"""
    try:
        result = subprocess.run(['getmac', '/fo', 'csv', '/nh'], 
                              capture_output=True, text=True, shell=True)
        
        mac_addresses = []
        for line in result.stdout.strip().split('\n'):
            if line and ',' in line:
                parts = line.split(',')
                if len(parts) >= 2:
                    mac = parts[0].strip('"')
                    adapter = parts[1].strip('"')
                    if mac and mac != 'N/A':
                        mac_addresses.append({
                            'mac': mac,
                            'adapter': adapter
                        })
        
        return mac_addresses
    except Exception as e:
        return f"Error: {e}"

def get_disk_info():
    """Obtiene información de discos"""
    try:
        result = subprocess.run(['wmic', 'diskdrive', 'get', 'serialnumber,model', '/format:csv'], 
                              capture_output=True, text=True, shell=True)
        
        disks = []
        lines = result.stdout.strip().split('\n')[1:]  # Skip header
        
        for line in lines:
            if line.strip() and ',' in line:
                parts = line.split(',')
                if len(parts) >= 3:
                    model = parts[1].strip()
                    serial = parts[2].strip()
                    if model and serial:
                        disks.append({
                            'model': model,
                            'serial': serial
                        })
        
        return disks
    except Exception as e:
        return f"Error: {e}"

def get_motherboard_info():
    """Obtiene información de la placa madre"""
    try:
        result = subprocess.run(['wmic', 'baseboard', 'get', 'serialnumber,manufacturer,product', '/format:csv'], 
                              capture_output=True, text=True, shell=True)
        
        lines = result.stdout.strip().split('\n')[1:]  # Skip header
        
        for line in lines:
            if line.strip() and ',' in line:
                parts = line.split(',')
                if len(parts) >= 4:
                    manufacturer = parts[1].strip()
                    product = parts[2].strip()
                    serial = parts[3].strip()
                    return {
                        'manufacturer': manufacturer,
                        'product': product,
                        'serial': serial
                    }
        
        return "No data found"
    except Exception as e:
        return f"Error: {e}"

def get_windows_product_id():
    """Obtiene el Product ID de Windows"""
    try:
        with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Microsoft\Windows NT\CurrentVersion") as key:
            product_id, _ = winreg.QueryValueEx(key, "ProductId")
            return product_id
    except Exception as e:
        return f"Error: {e}"

def get_system_uuid():
    """Obtiene el UUID del sistema"""
    try:
        result = subprocess.run(['wmic', 'csproduct', 'get', 'uuid', '/format:csv'], 
                              capture_output=True, text=True, shell=True)
        
        lines = result.stdout.strip().split('\n')[1:]  # Skip header
        
        for line in lines:
            if line.strip() and ',' in line:
                parts = line.split(',')
                if len(parts) >= 2:
                    uuid = parts[1].strip()
                    if uuid:
                        return uuid
        
        return "No UUID found"
    except Exception as e:
        return f"Error: {e}"

def check_z3x_registry_entries():
    """Busca entradas específicas de Z3X en el registro"""
    z3x_entries = {}
    
    # Buscar en HKEY_CURRENT_USER
    try:
        with winreg.OpenKey(winreg.HKEY_CURRENT_USER, r"SOFTWARE\Z3X") as key:
            z3x_entries['HKCU_Z3X'] = {}
            
            # Enumerar valores
            i = 0
            while True:
                try:
                    value_name, value_data, value_type = winreg.EnumValue(key, i)
                    z3x_entries['HKCU_Z3X'][value_name] = str(value_data)
                    i += 1
                except WindowsError:
                    break
    except Exception:
        pass
    
    # Buscar en HKEY_LOCAL_MACHINE
    try:
        with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Z3X") as key:
            z3x_entries['HKLM_Z3X'] = {}
            
            # Enumerar valores
            i = 0
            while True:
                try:
                    value_name, value_data, value_type = winreg.EnumValue(key, i)
                    z3x_entries['HKLM_Z3X'][value_name] = str(value_data)
                    i += 1
                except WindowsError:
                    break
    except Exception:
        pass
    
    return z3x_entries

def generate_hardware_fingerprint():
    """Genera un fingerprint completo del hardware"""
    
    print("="*60)
    print("🔍 EXTRAYENDO INFORMACIÓN DE HARDWARE PARA Z3X")
    print("="*60)
    
    hardware_info = {
        'timestamp': datetime.now().isoformat(),
        'system': {
            'platform': platform.platform(),
            'machine': platform.machine(),
            'processor': platform.processor(),
            'node': platform.node()
        },
        'critical_identifiers': {},
        'z3x_registry': {},
        'network': {},
        'storage': {},
        'system_info': {}
    }
    
    print("📋 Recopilando identificadores críticos...")
    
    # Machine GUID (MUY IMPORTANTE para Z3X)
    machine_guid = get_machine_guid()
    hardware_info['critical_identifiers']['machine_guid'] = machine_guid
    print(f"🔑 Machine GUID: {machine_guid}")
    
    # CPU Information
    cpu_info = get_cpu_info()
    hardware_info['critical_identifiers']['cpu'] = cpu_info
    if isinstance(cpu_info, dict):
        print(f"💻 CPU: {cpu_info.get('name', 'Unknown')}")
        print(f"🆔 CPU ID: {cpu_info.get('identifier', 'Unknown')}")
    
    # BIOS Information
    bios_info = get_bios_info()
    hardware_info['critical_identifiers']['bios'] = bios_info
    if isinstance(bios_info, dict):
        print(f"🔧 BIOS: {bios_info.get('vendor', 'Unknown')} {bios_info.get('version', 'Unknown')}")
    
    # MAC Addresses (CRÍTICO para Z3X)
    mac_addresses = get_mac_addresses()
    hardware_info['network']['mac_addresses'] = mac_addresses
    if isinstance(mac_addresses, list) and mac_addresses:
        print(f"🌐 MAC Addresses encontradas: {len(mac_addresses)}")
        for mac_info in mac_addresses[:3]:  # Mostrar solo las primeras 3
            print(f"   - {mac_info['mac']} ({mac_info['adapter']})")
    
    # Disk Information
    disk_info = get_disk_info()
    hardware_info['storage']['disks'] = disk_info
    if isinstance(disk_info, list) and disk_info:
        print(f"💾 Discos encontrados: {len(disk_info)}")
        for disk in disk_info[:2]:  # Mostrar solo los primeros 2
            print(f"   - {disk['model']}: {disk['serial']}")
    
    # Motherboard Information
    motherboard_info = get_motherboard_info()
    hardware_info['critical_identifiers']['motherboard'] = motherboard_info
    if isinstance(motherboard_info, dict):
        print(f"🔌 Motherboard: {motherboard_info.get('manufacturer', 'Unknown')} {motherboard_info.get('product', 'Unknown')}")
    
    # Windows Product ID
    product_id = get_windows_product_id()
    hardware_info['system_info']['windows_product_id'] = product_id
    print(f"🪟 Windows Product ID: {product_id}")
    
    # System UUID
    system_uuid = get_system_uuid()
    hardware_info['system_info']['system_uuid'] = system_uuid
    print(f"🆔 System UUID: {system_uuid}")
    
    # Z3X Registry Entries
    print(f"\n🔍 Buscando entradas Z3X en el registro...")
    z3x_registry = check_z3x_registry_entries()
    hardware_info['z3x_registry'] = z3x_registry
    
    if z3x_registry:
        print(f"✅ Encontradas entradas Z3X en el registro:")
        for key, values in z3x_registry.items():
            print(f"   📁 {key}: {len(values)} valores")
            for name, value in values.items():
                print(f"      - {name}: {value}")
    else:
        print(f"❌ No se encontraron entradas Z3X específicas en el registro")
    
    # Guardar información
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f"z3x_hardware_fingerprint_{timestamp}.json"
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(hardware_info, f, indent=2, ensure_ascii=False)
    
    # Generar resumen crítico
    print(f"\n" + "="*60)
    print("🚨 IDENTIFICADORES CRÍTICOS PARA Z3X")
    print("="*60)
    
    critical_ids = []
    
    if machine_guid and "Error" not in str(machine_guid):
        critical_ids.append(f"Machine GUID: {machine_guid}")
    
    if isinstance(mac_addresses, list) and mac_addresses:
        primary_mac = mac_addresses[0]['mac']
        critical_ids.append(f"Primary MAC: {primary_mac}")
    
    if isinstance(cpu_info, dict) and cpu_info.get('identifier'):
        critical_ids.append(f"CPU ID: {cpu_info['identifier']}")
    
    if isinstance(disk_info, list) and disk_info:
        primary_disk = disk_info[0]['serial']
        critical_ids.append(f"Primary Disk: {primary_disk}")
    
    if system_uuid and "Error" not in str(system_uuid):
        critical_ids.append(f"System UUID: {system_uuid}")
    
    print("Estos son los identificadores que Z3X probablemente usa:")
    for i, cid in enumerate(critical_ids, 1):
        print(f"{i}. {cid}")
    
    print(f"\n💾 Información completa guardada en: {output_file}")
    
    # Generar recomendaciones
    print(f"\n" + "="*60)
    print("💡 RECOMENDACIONES PARA RECUPERAR ACCESO")
    print("="*60)
    
    print("Para recuperar el acceso a tu cuenta Z3X:")
    print("1. 🔄 Cambiar MAC address del adaptador principal")
    print("2. 🔄 Considerar cambiar Machine GUID (requiere herramientas especiales)")
    print("3. 💻 Usar una máquina virtual con hardware diferente")
    print("4. 📞 Contactar soporte Z3X con nueva información de hardware")
    print("5. 🔧 Usar herramientas de spoofing de hardware")
    
    print(f"\n⚠️  IMPORTANTE: Hacer backup antes de modificar cualquier identificador!")
    print("="*60)
    
    return hardware_info

if __name__ == "__main__":
    print("Z3X Hardware ID Extractor")
    print("=========================")
    print("Esta herramienta extrae TODA la información de hardware")
    print("que Z3X Samstool usa para identificar tu PC.")
    print()
    
    try:
        hardware_info = generate_hardware_fingerprint()
        print(f"\n✅ Extracción completada exitosamente.")
        
    except Exception as e:
        print(f"\n❌ Error durante la extracción: {e}")
        sys.exit(1)
