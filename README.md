# Z3X Samstool Monitor

**Complete monitoring solution to intercept Z3X Samstool communications and identify PC fingerprinting methods.**

## 🚨 IMPORTANT SECURITY NOTICE

This tool is designed to help you understand how Z3X Samstool identifies your PC and what information it transmits. Use this information to protect your privacy and security.

## 🎯 What This Tool Does

1. **Network Traffic Interception** - Captures all network communications from Z3X Samstool
2. **Hardware Fingerprinting Detection** - Identifies what hardware information Z3X collects
3. **Authentication Flow Monitoring** - Monitors login processes and credential transmission
4. **Real-time Dashboard** - Web interface to view intercepted data live
5. **Security Analysis** - Generates reports on potential vulnerabilities

## 📋 Prerequisites

- Windows 10/11
- Python 3.8 or higher
- Administrator privileges (recommended)
- Z3X Samstool installed

## 🚀 Quick Start

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Run Complete Monitoring

```bash
python z3x_monitor.py --mode full
```

This will:
- Collect your PC's hardware fingerprint
- Start network monitoring
- Launch authentication monitoring
- Open web dashboard at http://127.0.0.1:5000

### 3. Configure Z3X Samstool

For HTTPS interception, configure Z3X to use proxy:
- HTTP Proxy: `127.0.0.1:8889`
- HTTPS Proxy: `127.0.0.1:8889`

### 4. Run Z3X Samstool

Start Z3X Samstool and attempt to login. The monitor will capture:
- All network connections
- Authentication attempts
- Hardware identification data
- Transmitted credentials

### 5. View Results

- **Real-time monitoring**: http://127.0.0.1:5000
- **Stop monitoring**: Press Ctrl+C
- **Analysis report**: Generated automatically when stopping

## 🔧 Individual Components

### Hardware Fingerprint Detection

```bash
python hardware_fingerprint_detector.py
```

Collects all possible hardware identifiers:
- CPU serial numbers
- MAC addresses
- Disk serial numbers
- BIOS information
- Windows Machine GUID
- Memory module details

### Network Traffic Monitoring

```bash
python network_interceptor.py
```

Monitors network connections and captures:
- Process-to-IP mappings
- Connection timestamps
- Port usage patterns
- Remote server communications

### Authentication Flow Monitoring

```bash
python auth_flow_monitor.py
```

Intercepts HTTPS traffic to capture:
- Login credentials
- Authentication tokens
- Device identification data
- API communications

### Data Analysis

```bash
python data_analyzer.py
```

Generates comprehensive reports including:
- Security vulnerability assessment
- Privacy risk analysis
- Network pattern analysis
- Mitigation recommendations

## 📊 Dashboard Features

The web dashboard provides:

- **Real-time monitoring** of all intercepted communications
- **Search and filtering** capabilities
- **Security alerts** for credential exposures
- **Hardware fingerprint viewer**
- **Network topology visualization**
- **Export functionality** (CSV, XML, JSON)

## 🛡️ Security Findings

The tool will identify:

### Critical Identifiers
- **CPU Processor ID** - Unique hardware identifier
- **Machine GUID** - Windows unique identifier
- **MAC Addresses** - Network adapter identifiers
- **BIOS Serial Numbers** - Motherboard identifiers
- **Disk Serial Numbers** - Storage device identifiers

### Network Communications
- **Remote servers** Z3X connects to
- **Authentication endpoints**
- **Data transmission methods**
- **Encryption status**

### Privacy Risks
- **Unencrypted transmissions**
- **Credential exposures**
- **Device fingerprinting extent**
- **Tracking capabilities**

## 🔒 Protection Recommendations

Based on analysis results:

1. **Use VPN** to mask IP address
2. **Spoof MAC addresses** before running Z3X
3. **Run in Virtual Machine** with different hardware profile
4. **Block Z3X domains** at firewall level
5. **Monitor registry changes** during execution
6. **Use process sandboxing**

## 📁 Output Files

The tool generates:

- `hardware_fingerprint_YYYYMMDD_HHMMSS.json` - Hardware identification data
- `z3x_captured_data_YYYYMMDD_HHMMSS.json` - Network traffic logs
- `z3x_auth_data_YYYYMMDD_HHMMSS.json` - Authentication flow data
- `z3x_security_report_YYYYMMDD_HHMMSS.json` - Comprehensive analysis
- `z3x_analysis_export_YYYYMMDD_HHMMSS.csv` - Data export for analysis
- `visualizations/` - Charts and graphs

## ⚙️ Advanced Usage

### Custom Dashboard Port

```bash
python z3x_monitor.py --mode full --port 8080
```

### Individual Component Testing

```bash
# Test fingerprint detection only
python z3x_monitor.py --mode fingerprint

# Test network monitoring only
python z3x_monitor.py --mode network

# Run analysis on existing data
python z3x_monitor.py --mode analyze
```

### Proxy Configuration

For manual proxy setup:
- Network proxy: Port 8888
- Auth proxy: Port 8889

## 🐛 Troubleshooting

### Common Issues

1. **"Access Denied" errors**
   - Run as Administrator
   - Check Windows Defender/Antivirus

2. **No network data captured**
   - Ensure Z3X is running during monitoring
   - Check firewall settings
   - Verify admin privileges

3. **Dashboard not accessible**
   - Check if port is already in use
   - Try different port with `--port` option
   - Verify firewall allows local connections

4. **HTTPS interception fails**
   - Install mitmproxy certificates
   - Configure Z3X proxy settings
   - Check proxy port availability

## ⚠️ Legal Disclaimer

This tool is for educational and security research purposes only. Use only on systems you own or have explicit permission to monitor. The authors are not responsible for any misuse of this software.

## 🤝 Contributing

Contributions welcome! Please:
1. Fork the repository
2. Create feature branch
3. Submit pull request

## 📞 Support

For issues or questions:
1. Check troubleshooting section
2. Review log files for errors
3. Create GitHub issue with details

## 🔄 Updates

Check for updates regularly as Z3X may change its communication methods.

---

**Remember**: The goal is to understand and protect against unauthorized PC identification and data transmission. Use this knowledge responsibly to enhance your privacy and security.
