
# GUÍA COMPLETA PARA MÁQUINA VIRTUAL Z3X

## ¿Por qué usar una máquina virtual?
- Hardware completamente diferente al sistema principal
- Z3X verá la VM como un PC totalmente distinto
- Fácil de resetear si hay problemas
- Aislamiento total del sistema principal

## OPCIÓN 1: VirtualBox (GRATIS)

### Paso 1: Descargar e instalar
1. Ve a: https://www.virtualbox.org/
2. Descarga VirtualBox para Windows
3. Instala con configuración por defecto

### Paso 2: Crear máquina virtual
1. Abre VirtualBox
2. Clic en "Nueva"
3. Nombre: "Z3X-Recovery"
4. Tipo: Microsoft Windows
5. Versión: Windows 10 (64-bit)
6. RAM: 4096 MB (4GB)
7. Crear disco duro virtual: 50GB
8. Tipo de archivo: VDI
9. Almacenamiento: Asignado dinámicamente

### Paso 3: Configurar hardware único
1. Selecciona la VM creada
2. Clic en "Configuración"
3. Sistema → Procesador: 2 CPUs
4. Red → Adaptador 1: NAT
5. Avanzado → Generar nueva MAC

### Paso 4: Instalar Windows
1. Descargar ISO de Windows 10/11
2. En VirtualBox: Configuración → Almacenamiento
3. Agregar ISO como unidad óptica
4. Iniciar VM e instalar Windows

### Paso 5: Instalar Z3X
1. Dentro de la VM, descargar Z3X Samstool
2. Instalar normalmente
3. Usar tu cuenta Z3X

## OPCIÓN 2: VMware Workstation (MEJOR RENDIMIENTO)

### Paso 1: Descargar VMware
1. Ve a: https://www.vmware.com/products/workstation-pro.html
2. Descarga versión de prueba
3. Instala con configuración por defecto

### Paso 2: Crear VM
1. Abre VMware Workstation
2. File → New Virtual Machine
3. Typical configuration
4. Installer disc image: Seleccionar ISO de Windows
5. Guest OS: Windows 10 x64
6. VM Name: "Z3X-Recovery"
7. Disk size: 50GB
8. RAM: 4GB

### Paso 3: Configurar hardware
1. Edit virtual machine settings
2. Network Adapter: NAT
3. Advanced → Generate new MAC
4. Processors: 2 cores

## VENTAJAS DE CADA MÉTODO

### VirtualBox:
✅ Completamente gratis
✅ Fácil de usar
✅ Buena compatibilidad
❌ Rendimiento menor

### VMware:
✅ Mejor rendimiento
✅ Más características
✅ Mejor soporte 3D
❌ Costo (versión completa)

## CONFIGURACIÓN RECOMENDADA PARA Z3X

### Especificaciones mínimas:
- RAM: 4GB
- Disco: 50GB
- CPU: 2 cores
- Red: NAT
- 3D: Habilitado

### Configuración de red:
- Tipo: NAT (recomendado)
- Alternativa: Bridge (si NAT no funciona)

## PASOS DESPUÉS DE CREAR LA VM

1. **Instalar Windows completamente**
2. **Activar Windows** (usar clave legítima)
3. **Instalar VMware Tools** o **VirtualBox Guest Additions**
4. **Configurar red** y verificar conectividad
5. **Descargar Z3X Samstool** dentro de la VM
6. **Instalar Z3X** normalmente
7. **Intentar login** con tu cuenta

## SOLUCIÓN DE PROBLEMAS

### Si Z3X no funciona en VM:
1. Verificar que la virtualización esté habilitada
2. Asignar más RAM (6-8GB)
3. Habilitar aceleración 3D
4. Cambiar tipo de red (NAT ↔ Bridge)
5. Generar nueva MAC address

### Si hay problemas de rendimiento:
1. Aumentar RAM asignada
2. Aumentar CPUs virtuales
3. Habilitar aceleración de hardware
4. Cerrar programas innecesarios en host

## BACKUP Y SNAPSHOTS

### Crear snapshot antes de usar Z3X:
1. VM → Snapshot → Take Snapshot
2. Nombre: "Windows-Limpio"
3. Descripción: "Antes de instalar Z3X"

### Si algo sale mal:
1. VM → Snapshot → Revert to Snapshot
2. Seleccionar "Windows-Limpio"
3. Intentar nuevamente

## CONSIDERACIONES IMPORTANTES

⚠️ **Licencias**: Necesitas licencia válida de Windows para la VM
⚠️ **Rendimiento**: La VM será más lenta que el sistema nativo
⚠️ **Recursos**: Asegúrate de tener suficiente RAM y espacio en disco
⚠️ **Drivers**: Instala Guest Additions/Tools para mejor rendimiento

## ALTERNATIVAS SI LA VM NO FUNCIONA

1. **Usar otro PC físico** con hardware diferente
2. **Cambiar hardware físico** (nueva tarjeta de red, etc.)
3. **Contactar soporte Z3X** con nueva información de hardware
4. **Usar herramientas de spoofing** más avanzadas

¡La máquina virtual es la opción MÁS SEGURA y EFECTIVA!
