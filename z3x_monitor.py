#!/usr/bin/env python3
"""
Z3X Samstool Monitor - Main Orchestrator
Complete monitoring solution for Z3X Samstool communications and PC identification
"""

import sys
import os
import threading
import time
import logging
import argparse
from datetime import datetime

# Import our modules
from network_interceptor import NetworkInterceptor
from hardware_fingerprint_detector import HardwareFingerprintDetector
from auth_flow_monitor import AuthFlowMonitor
from dashboard import start_dashboard
from data_analyzer import DataAnalyzer

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'z3x_monitor_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class Z3XMonitor:
    def __init__(self):
        self.network_interceptor = NetworkInterceptor()
        self.fingerprint_detector = HardwareFingerprintDetector()
        self.auth_monitor = AuthFlowMonitor()
        self.data_analyzer = DataAnalyzer()
        self.dashboard_thread = None
        self.monitoring_active = False
        
    def check_admin_privileges(self) -> bool:
        """Check if running with administrator privileges"""
        try:
            import ctypes
            return ctypes.windll.shell32.IsUserAnAdmin()
        except:
            return False
    
    def collect_baseline_fingerprint(self):
        """Collect baseline hardware fingerprint"""
        logger.info("Collecting baseline hardware fingerprint...")
        
        try:
            fingerprint_data = self.fingerprint_detector.collect_all_fingerprints()
            filename = self.fingerprint_detector.save_fingerprint_data()
            
            logger.info(f"Baseline fingerprint saved to: {filename}")
            
            # Display critical identifiers
            print("\n" + "="*60)
            print("CRITICAL PC IDENTIFIERS DETECTED:")
            print("="*60)
            
            # CPU ID
            cpu_id = fingerprint_data.get('cpu', {}).get('processor_id')
            if cpu_id:
                print(f"CPU ID: {cpu_id}")
            
            # Machine GUID
            machine_guid = fingerprint_data.get('registry', {}).get('machine_guid')
            if machine_guid:
                print(f"Machine GUID: {machine_guid}")
            
            # BIOS Serial
            bios_serial = fingerprint_data.get('bios', {}).get('serial_number')
            if bios_serial:
                print(f"BIOS Serial: {bios_serial}")
            
            # MAC Addresses
            mac_addresses = []
            for adapter in fingerprint_data.get('network', {}).get('adapters', []):
                if adapter.get('mac_address'):
                    mac_addresses.append(adapter['mac_address'])
            
            if mac_addresses:
                print(f"MAC Addresses: {', '.join(mac_addresses)}")
            
            # Disk Serials
            disk_serials = []
            for disk in fingerprint_data.get('disk', {}).get('physical_drives', []):
                if disk.get('serial_number'):
                    disk_serials.append(disk['serial_number'])
            
            if disk_serials:
                print(f"Disk Serials: {', '.join(disk_serials)}")
            
            print(f"\nFingerprint Hash: {fingerprint_data.get('fingerprint_hash', 'N/A')}")
            print("="*60)
            
            return filename
            
        except Exception as e:
            logger.error(f"Error collecting fingerprint: {e}")
            return None
    
    def start_network_monitoring(self):
        """Start network traffic monitoring"""
        logger.info("Starting network monitoring...")
        
        try:
            # Start in separate thread
            network_thread = threading.Thread(
                target=self.network_interceptor.start_interception,
                daemon=True
            )
            network_thread.start()
            
            logger.info("Network monitoring started successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error starting network monitoring: {e}")
            return False
    
    def start_auth_monitoring(self):
        """Start authentication flow monitoring"""
        logger.info("Starting authentication monitoring...")
        
        try:
            # Start in separate thread
            auth_thread = threading.Thread(
                target=self.auth_monitor.start_monitoring,
                daemon=True
            )
            auth_thread.start()
            
            logger.info("Authentication monitoring started successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error starting auth monitoring: {e}")
            return False
    
    def start_dashboard(self, port=5000):
        """Start web dashboard"""
        logger.info(f"Starting web dashboard on port {port}...")
        
        try:
            self.dashboard_thread = threading.Thread(
                target=start_dashboard,
                args=('127.0.0.1', port),
                daemon=True
            )
            self.dashboard_thread.start()
            
            logger.info(f"Dashboard started at http://127.0.0.1:{port}")
            return True
            
        except Exception as e:
            logger.error(f"Error starting dashboard: {e}")
            return False
    
    def run_analysis(self):
        """Run data analysis on captured data"""
        logger.info("Running data analysis...")
        
        try:
            report_file = self.data_analyzer.run_full_analysis()
            logger.info(f"Analysis complete. Report: {report_file}")
            return report_file
            
        except Exception as e:
            logger.error(f"Error running analysis: {e}")
            return None
    
    def start_full_monitoring(self, dashboard_port=5000):
        """Start complete monitoring suite"""
        print("\n" + "="*60)
        print("Z3X SAMSTOOL COMPLETE MONITORING SUITE")
        print("="*60)
        print("This tool will intercept ALL communications between")
        print("Z3X Samstool and identify how it fingerprints your PC.")
        print("="*60)
        
        # Check admin privileges
        if not self.check_admin_privileges():
            print("\n⚠️  WARNING: Not running as Administrator!")
            print("Some monitoring features may not work properly.")
            print("For best results, run as Administrator.")
            input("\nPress Enter to continue anyway...")
        
        # Collect baseline fingerprint
        print("\n1. Collecting baseline hardware fingerprint...")
        fingerprint_file = self.collect_baseline_fingerprint()
        
        if fingerprint_file:
            print(f"✅ Fingerprint collected: {fingerprint_file}")
        else:
            print("❌ Failed to collect fingerprint")
        
        # Start dashboard
        print(f"\n2. Starting web dashboard on port {dashboard_port}...")
        if self.start_dashboard(dashboard_port):
            print(f"✅ Dashboard available at: http://127.0.0.1:{dashboard_port}")
        else:
            print("❌ Failed to start dashboard")
        
        # Start network monitoring
        print("\n3. Starting network traffic monitoring...")
        if self.start_network_monitoring():
            print("✅ Network monitoring active")
        else:
            print("❌ Failed to start network monitoring")
        
        # Start auth monitoring
        print("\n4. Starting authentication flow monitoring...")
        print("   Configure Z3X to use proxy: 127.0.0.1:8889")
        if self.start_auth_monitoring():
            print("✅ Authentication monitoring active")
        else:
            print("❌ Failed to start auth monitoring")
        
        self.monitoring_active = True
        
        print("\n" + "="*60)
        print("MONITORING ACTIVE - ALL SYSTEMS OPERATIONAL")
        print("="*60)
        print("Now run Z3X Samstool to capture its communications.")
        print(f"Monitor in real-time at: http://127.0.0.1:{dashboard_port}")
        print("\nPress Ctrl+C to stop monitoring and generate analysis...")
        print("="*60)
        
        try:
            while self.monitoring_active:
                time.sleep(1)
        except KeyboardInterrupt:
            self.stop_monitoring()
    
    def stop_monitoring(self):
        """Stop all monitoring and generate final analysis"""
        print("\n\nStopping monitoring...")
        self.monitoring_active = False
        
        # Stop network interceptor
        if hasattr(self.network_interceptor, 'running'):
            self.network_interceptor.stop_interception()
        
        # Stop auth monitor
        if hasattr(self.auth_monitor, 'running'):
            self.auth_monitor.stop_monitoring()
        
        print("Generating final analysis report...")
        report_file = self.run_analysis()
        
        if report_file:
            print(f"\n✅ Final analysis report: {report_file}")
            print("✅ Check 'visualizations' folder for charts")
            print("✅ CSV and XML exports generated")
        
        print("\nMonitoring session complete!")

def main():
    parser = argparse.ArgumentParser(description='Z3X Samstool Monitor')
    parser.add_argument('--mode', choices=['full', 'fingerprint', 'network', 'auth', 'dashboard', 'analyze'],
                       default='full', help='Monitoring mode')
    parser.add_argument('--port', type=int, default=5000, help='Dashboard port')
    parser.add_argument('--no-admin-check', action='store_true', help='Skip admin privilege check')
    
    args = parser.parse_args()
    
    monitor = Z3XMonitor()
    
    if args.mode == 'full':
        monitor.start_full_monitoring(args.port)
    elif args.mode == 'fingerprint':
        monitor.collect_baseline_fingerprint()
    elif args.mode == 'network':
        monitor.start_network_monitoring()
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            pass
    elif args.mode == 'auth':
        monitor.start_auth_monitoring()
    elif args.mode == 'dashboard':
        monitor.start_dashboard(args.port)
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            pass
    elif args.mode == 'analyze':
        monitor.run_analysis()

if __name__ == "__main__":
    main()
