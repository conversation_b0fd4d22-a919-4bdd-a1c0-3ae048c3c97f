# Z3X Recovery Tools - Recuperación de Cuenta Z3X Samstool

## 🎯 Objetivo

Este conjunto de herramientas está diseñado para ayudarte a **recuperar el acceso a tu cuenta Z3X Samstool** cuando alguien ha comprometido tu cuenta y no puedes iniciar sesión. Las herramientas analizan completamente cómo Z3X identifica tu PC y proporcionan soluciones específicas.

## 🚨 Situación que Resuelve

- ✅ Alguien robó tu cuenta Z3X y no puedes iniciar sesión
- ✅ Z3X dice que tu PC no está autorizado
- ✅ Necesitas entender cómo Z3X identifica tu hardware
- ✅ Quieres recuperar el acceso a tu cuenta legítima

## 🛠️ Herramientas Incluidas

### 1. **Z3X Recovery Tool** (`z3x_recovery_tool.py`)
**Herramienta principal** que coordina todo el proceso de recuperación.

**Funciones:**
- Análisis completo del sistema
- Generación de recomendaciones específicas
- Ejecución de acciones de recuperación
- Backup automático antes de cambios

### 2. **Z3X File Scanner** (`z3x_file_scanner.py`)
Busca **TODOS** los archivos relacionados con Z3X en tu sistema.

**Busca:**
- Archivos de configuración (.ini, .cfg)
- Archivos de licencia (.lic, .license)
- Archivos de log (.log, .txt)
- Ejecutables y DLLs de Z3X
- Archivos de datos (.dat, .bin)

### 3. **Z3X Config Analyzer** (`z3x_config_analyzer.py`)
Analiza archivos encontrados para extraer información crítica.

**Extrae:**
- Identificadores de hardware (HWID)
- Información de licencia
- Patrones de autenticación
- Datos encriptados

### 4. **Z3X Registry Scanner** (`z3x_registry_scanner.py`)
Escanea el registro de Windows buscando entradas Z3X.

**Encuentra:**
- Claves de registro Z3X
- Información de hardware del sistema
- Entradas de software instalado
- Servicios relacionados

## 🚀 Instalación Rápida

### Opción 1: Instalación Automática (Recomendada)

1. **Ejecutar como Administrador:**
   ```cmd
   install_recovery_tools.bat
   ```

2. **Iniciar herramienta principal:**
   ```cmd
   start_z3x_recovery.bat
   ```

### Opción 2: Instalación Manual

1. **Instalar Python** (si no lo tienes):
   - Descargar desde: https://python.org
   - Marcar "Add to PATH" durante instalación

2. **Instalar dependencias:**
   ```cmd
   pip install psutil wmi requests flask flask-socketio mitmproxy pandas matplotlib seaborn cryptography pywin32
   ```

3. **Ejecutar herramienta principal:**
   ```cmd
   python z3x_recovery_tool.py
   ```

## 📋 Uso Paso a Paso

### Paso 1: Análisis Completo
```cmd
python z3x_recovery_tool.py
```
- Seleccionar opción "1. Ejecutar análisis completo del sistema"
- Esperar 10-30 minutos (depende del tamaño del sistema)
- Revisar el reporte generado

### Paso 2: Revisar Resultados
El análisis genera varios archivos:
- `z3x_recovery_analysis_YYYYMMDD_HHMMSS.json` - Análisis completo
- `z3x_scan_report_YYYYMMDD_HHMMSS.json` - Archivos encontrados
- `z3x_config_analysis_YYYYMMDD_HHMMSS.json` - Análisis de configuración
- `z3x_registry_scan_YYYYMMDD_HHMMSS.json` - Escaneo del registro

### Paso 3: Implementar Recomendaciones
La herramienta proporciona recomendaciones específicas categorizadas por prioridad:

#### 🚨 **CRÍTICAS** (Implementar primero)
- Modificar identificadores de hardware
- Cambiar MAC addresses
- Usar máquina virtual

#### ⚠️ **ALTA PRIORIDAD**
- Limpiar archivos de configuración Z3X
- Eliminar entradas del registro
- Hacer backup de archivos importantes

#### 📋 **MEDIA PRIORIDAD**
- Usar VPN
- Contactar soporte Z3X
- Métodos alternativos

## 🔍 Qué Busca y Analiza

### Identificadores de Hardware
- **Machine GUID** - Identificador único de Windows
- **CPU ID** - Identificador del procesador
- **MAC Addresses** - Direcciones físicas de red
- **Disk Serial Numbers** - Números de serie de discos
- **BIOS Information** - Información del BIOS/UEFI
- **Motherboard Serial** - Serie de la placa madre

### Archivos Z3X
- Configuraciones en `Program Files`
- Datos de usuario en `AppData`
- Archivos temporales
- Logs de actividad
- Licencias y claves

### Registro de Windows
- Entradas de software Z3X
- Claves de hardware del sistema
- Servicios relacionados
- Información de instalación

## 🛡️ Seguridad y Backups

### Backups Automáticos
La herramienta crea automáticamente:
- Backup completo del registro
- Copia de archivos Z3X importantes
- Snapshot del estado actual del sistema

### Ubicaciones de Backup
```
z3x_recovery_backup_YYYYMMDD_HHMMSS/
├── registry_backup.reg
├── z3x_files/
└── system_info.json
```

## 🔧 Herramientas Individuales

### Escaneo Rápido de Archivos
```cmd
python z3x_file_scanner.py
```

### Análisis de Configuración
```cmd
python z3x_config_analyzer.py
```

### Escaneo del Registro
```cmd
python z3x_registry_scanner.py
```

### Detección de Hardware
```cmd
python hardware_fingerprint_detector.py
```

## 📊 Reportes Generados

### Reporte Principal
- **Archivos encontrados:** Lista completa de archivos Z3X
- **Identificadores críticos:** Hardware IDs que Z3X usa
- **Recomendaciones:** Acciones específicas para recuperar acceso
- **Prioridades:** Orden de implementación

### Archivos de Análisis
- **JSON detallado:** Toda la información técnica
- **Logs de actividad:** Registro de todas las acciones
- **Exports del registro:** Claves Z3X exportadas

## ⚠️ Advertencias Importantes

### Privilegios de Administrador
- **Requerido** para acceso completo al registro
- **Requerido** para modificar archivos del sistema
- **Recomendado** ejecutar como Administrador

### Antivirus
- Algunos antivirus pueden detectar las herramientas como "sospechosas"
- **Es normal** - las herramientas acceden a hardware y registro
- Agregar excepción si es necesario

### Legalidad
- **Solo usar en tu propio sistema**
- **Solo para recuperar tu propia cuenta**
- No usar para acceso no autorizado

## 🆘 Solución de Problemas

### Error: "No se encontraron archivos Z3X"
- Verificar que Z3X esté instalado
- Ejecutar como Administrador
- Buscar en ubicaciones personalizadas

### Error: "Sin permisos para registro"
- Ejecutar como Administrador
- Verificar UAC de Windows
- Usar cuenta con privilegios

### Error: "Módulo no encontrado"
- Ejecutar: `pip install -r requirements.txt`
- Verificar instalación de Python
- Usar entorno virtual si es necesario

## 📞 Soporte

### Archivos de Log
Los logs se guardan en:
- `logs/z3x_recovery.log`
- `logs/scanner.log`
- `logs/analyzer.log`

### Información del Sistema
Incluir en reportes de problemas:
- Versión de Windows
- Versión de Python
- Archivos de log generados
- Mensaje de error específico

## 🎯 Próximos Pasos Después del Análisis

1. **Revisar recomendaciones críticas**
2. **Implementar cambios de hardware**
3. **Limpiar archivos y registro Z3X**
4. **Probar acceso a Z3X**
5. **Contactar soporte Z3X si es necesario**

---

## 📝 Notas Finales

Esta herramienta está diseñada específicamente para ayudarte a recuperar el acceso a tu cuenta Z3X Samstool legítima. Úsala responsablemente y solo en tu propio sistema.

**¡Buena suerte recuperando tu cuenta!** 🍀
