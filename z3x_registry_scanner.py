#!/usr/bin/env python3
"""
Z3X Registry Scanner - Busca entradas relacionadas con Z3X en el registro de Windows
"""

import winreg
import json
import logging
from datetime import datetime

logger = logging.getLogger(__name__)

class Z3XRegistryScanner:
    def __init__(self):
        self.registry_entries = {}
        self.z3x_keys = []
        self.hardware_keys = {}
        
    def scan_registry_key(self, hive, key_path, search_terms):
        """Escanea una clave específica del registro"""
        found_entries = {}
        
        try:
            with winreg.OpenKey(hive, key_path) as key:
                # Obtener información de la clave
                key_info = winreg.QueryInfoKey(key)
                num_subkeys = key_info[0]
                num_values = key_info[1]
                
                # Escanear valores
                for i in range(num_values):
                    try:
                        value_name, value_data, value_type = winreg.EnumValue(key, i)
                        
                        # Buscar términos relacionados con Z3X
                        if any(term.lower() in value_name.lower() for term in search_terms):
                            found_entries[value_name] = {
                                'data': str(value_data),
                                'type': value_type,
                                'key_path': key_path
                            }
                            print(f"🔍 Encontrado: {key_path}\\{value_name} = {value_data}")
                        
                        # Buscar en el contenido del valor
                        if isinstance(value_data, str) and any(term.lower() in value_data.lower() for term in search_terms):
                            found_entries[f"{value_name}_content"] = {
                                'data': str(value_data),
                                'type': value_type,
                                'key_path': key_path,
                                'note': 'Found in value content'
                            }
                            print(f"🔍 Contenido encontrado: {key_path}\\{value_name} contiene términos Z3X")
                    
                    except Exception as e:
                        continue
                
                # Escanear subclaves
                for i in range(num_subkeys):
                    try:
                        subkey_name = winreg.EnumKey(key, i)
                        
                        # Buscar nombres de subclave relacionados
                        if any(term.lower() in subkey_name.lower() for term in search_terms):
                            subkey_path = f"{key_path}\\{subkey_name}"
                            self.z3x_keys.append(subkey_path)
                            print(f"📁 Subclave Z3X encontrada: {subkey_path}")
                            
                            # Escanear recursivamente la subclave
                            subkey_entries = self.scan_registry_key(hive, subkey_path, search_terms)
                            found_entries.update(subkey_entries)
                    
                    except Exception as e:
                        continue
        
        except FileNotFoundError:
            pass
        except PermissionError:
            logger.warning(f"Sin permisos para acceder a: {key_path}")
        except Exception as e:
            logger.error(f"Error escaneando {key_path}: {e}")
        
        return found_entries
    
    def scan_hardware_registry(self):
        """Escanea claves de hardware del registro"""
        hardware_paths = [
            r"HARDWARE\DESCRIPTION\System\CentralProcessor\0",
            r"HARDWARE\DESCRIPTION\System\BIOS",
            r"SYSTEM\CurrentControlSet\Control\SystemInformation",
            r"SOFTWARE\Microsoft\Cryptography",
            r"SOFTWARE\Microsoft\Windows NT\CurrentVersion",
            r"SYSTEM\CurrentControlSet\Enum\IDE",
            r"SYSTEM\CurrentControlSet\Enum\SCSI",
            r"SYSTEM\CurrentControlSet\Services\Disk\Enum"
        ]
        
        print("\n🔧 Escaneando información de hardware en el registro...")
        
        for path in hardware_paths:
            try:
                with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, path) as key:
                    key_info = winreg.QueryInfoKey(key)
                    num_values = key_info[1]
                    
                    path_data = {}
                    
                    for i in range(num_values):
                        try:
                            value_name, value_data, value_type = winreg.EnumValue(key, i)
                            path_data[value_name] = str(value_data)
                            
                            # Identificar valores importantes
                            important_values = ['ProcessorNameString', 'Identifier', 'MachineGuid', 
                                              'ProductId', 'SystemBiosVersion', 'BaseBoardProduct']
                            
                            if value_name in important_values:
                                print(f"🔑 Hardware ID: {value_name} = {value_data}")
                        
                        except Exception as e:
                            continue
                    
                    if path_data:
                        self.hardware_keys[path] = path_data
            
            except Exception as e:
                continue
    
    def scan_software_registry(self):
        """Escanea claves de software instalado"""
        software_paths = [
            r"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall",
            r"SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall",
            r"SOFTWARE\Classes\Installer\Products"
        ]
        
        z3x_software = {}
        search_terms = ['z3x', 'samstool', 'samsung', 'box']
        
        print("\n💿 Escaneando software instalado...")
        
        for base_path in software_paths:
            try:
                with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, base_path) as key:
                    key_info = winreg.QueryInfoKey(key)
                    num_subkeys = key_info[0]
                    
                    for i in range(num_subkeys):
                        try:
                            subkey_name = winreg.EnumKey(key, i)
                            subkey_path = f"{base_path}\\{subkey_name}"
                            
                            with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, subkey_path) as subkey:
                                subkey_info = winreg.QueryInfoKey(subkey)
                                num_values = subkey_info[1]
                                
                                software_info = {}
                                is_z3x_related = False
                                
                                for j in range(num_values):
                                    try:
                                        value_name, value_data, value_type = winreg.EnumValue(subkey, j)
                                        software_info[value_name] = str(value_data)
                                        
                                        # Buscar términos Z3X en nombres y valores
                                        if (any(term.lower() in value_name.lower() for term in search_terms) or
                                            (isinstance(value_data, str) and any(term.lower() in value_data.lower() for term in search_terms))):
                                            is_z3x_related = True
                                    
                                    except Exception as e:
                                        continue
                                
                                if is_z3x_related:
                                    z3x_software[subkey_path] = software_info
                                    display_name = software_info.get('DisplayName', 'Unknown')
                                    print(f"📦 Software Z3X encontrado: {display_name}")
                        
                        except Exception as e:
                            continue
            
            except Exception as e:
                continue
        
        return z3x_software
    
    def scan_user_registry(self):
        """Escanea el registro del usuario actual"""
        user_paths = [
            r"SOFTWARE",
            r"SOFTWARE\Classes",
            r"AppEvents\Schemes\Apps"
        ]
        
        search_terms = ['z3x', 'samstool', 'samsung', 'box', 'hwid']
        user_entries = {}
        
        print("\n👤 Escaneando registro de usuario...")
        
        for path in user_paths:
            entries = self.scan_registry_key(winreg.HKEY_CURRENT_USER, path, search_terms)
            if entries:
                user_entries[path] = entries
        
        return user_entries
    
    def scan_services_registry(self):
        """Escanea servicios del sistema"""
        services_path = r"SYSTEM\CurrentControlSet\Services"
        search_terms = ['z3x', 'samstool', 'samsung', 'box']
        
        print("\n🔧 Escaneando servicios del sistema...")
        
        services_entries = self.scan_registry_key(winreg.HKEY_LOCAL_MACHINE, services_path, search_terms)
        return services_entries
    
    def export_registry_keys(self, output_file):
        """Exporta claves Z3X encontradas"""
        if not self.z3x_keys:
            return
        
        print(f"\n💾 Exportando claves del registro a {output_file}...")
        
        try:
            import subprocess
            
            for key_path in self.z3x_keys:
                try:
                    # Exportar usando reg export
                    export_file = f"z3x_registry_export_{key_path.replace('\\', '_')}.reg"
                    cmd = f'reg export "HKEY_LOCAL_MACHINE\\{key_path}" "{export_file}"'
                    
                    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
                    if result.returncode == 0:
                        print(f"✅ Exportado: {export_file}")
                    else:
                        print(f"❌ Error exportando: {key_path}")
                
                except Exception as e:
                    logger.error(f"Error exportando {key_path}: {e}")
        
        except Exception as e:
            logger.error(f"Error en exportación: {e}")
    
    def scan_complete_registry(self):
        """Escaneo completo del registro"""
        print("="*60)
        print("ESCANEANDO REGISTRO DE WINDOWS PARA ENTRADAS Z3X")
        print("="*60)
        
        results = {
            'timestamp': datetime.now().isoformat(),
            'hardware_info': {},
            'z3x_software': {},
            'user_entries': {},
            'services_entries': {},
            'z3x_keys_found': [],
            'summary': {}
        }
        
        # Escanear información de hardware
        self.scan_hardware_registry()
        results['hardware_info'] = self.hardware_keys
        
        # Escanear software Z3X
        results['z3x_software'] = self.scan_software_registry()
        
        # Escanear registro de usuario
        results['user_entries'] = self.scan_user_registry()
        
        # Escanear servicios
        results['services_entries'] = self.scan_services_registry()
        
        # Claves Z3X encontradas
        results['z3x_keys_found'] = self.z3x_keys
        
        # Generar resumen
        results['summary'] = {
            'hardware_keys_found': len(self.hardware_keys),
            'z3x_software_entries': len(results['z3x_software']),
            'user_entries_found': len(results['user_entries']),
            'services_entries_found': len(results['services_entries']),
            'total_z3x_keys': len(self.z3x_keys)
        }
        
        # Guardar resultados
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = f"z3x_registry_scan_{timestamp}.json"
        
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        # Exportar claves encontradas
        if self.z3x_keys:
            self.export_registry_keys("z3x_registry_exports")
        
        # Mostrar resumen
        print("\n" + "="*60)
        print("RESUMEN DEL ESCANEO DEL REGISTRO")
        print("="*60)
        print(f"Claves de hardware encontradas: {results['summary']['hardware_keys_found']}")
        print(f"Entradas de software Z3X: {results['summary']['z3x_software_entries']}")
        print(f"Entradas de usuario: {results['summary']['user_entries_found']}")
        print(f"Entradas de servicios: {results['summary']['services_entries_found']}")
        print(f"Total claves Z3X encontradas: {results['summary']['total_z3x_keys']}")
        
        if self.z3x_keys:
            print(f"\n📁 CLAVES Z3X ENCONTRADAS:")
            for key in self.z3x_keys[:10]:  # Mostrar primeras 10
                print(f"  - {key}")
        
        print(f"\nResultados guardados en: {results_file}")
        print("="*60)
        
        return results

if __name__ == "__main__":
    scanner = Z3XRegistryScanner()
    
    print("Z3X Registry Scanner")
    print("====================")
    print("Este escáner buscará todas las entradas relacionadas con Z3X")
    print("en el registro de Windows, incluyendo información de hardware.")
    print("\nEsto puede tomar varios minutos...")
    print("\nPresiona Enter para comenzar...")
    input()
    
    try:
        results = scanner.scan_complete_registry()
        
        if results:
            print(f"\n✅ Escaneo del registro completado.")
            print("Revisa los archivos generados para más detalles.")
        else:
            print(f"\n❌ Error durante el escaneo del registro.")
            
    except Exception as e:
        print(f"\nError: {e}")
        logger.error(f"Error en escaneo del registro: {e}")
