#!/usr/bin/env python3
"""
RECUPERAR CUENTA Z3X - Script automático para recuperar acceso a cuenta Z3X robada
"""

import os
import sys
import json
import winreg
import subprocess
import shutil
import random
import string
import uuid
from datetime import datetime

def check_admin():
    """Verifica privilegios de administrador"""
    try:
        import ctypes
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False

def create_backup():
    """Crea backup completo del sistema"""
    backup_dir = f"BACKUP_Z3X_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    os.makedirs(backup_dir, exist_ok=True)
    
    print(f"💾 Creando backup en: {backup_dir}")
    
    # Backup del registro
    try:
        subprocess.run(f'reg export HKLM "{backup_dir}\\registro_completo.reg"', shell=True, check=True)
        print("✅ Backup del registro creado")
    except:
        print("❌ Error en backup del registro")
    
    # Backup de archivos Z3X
    try:
        z3x_appdata = os.path.expanduser(r"~\AppData\Roaming\z3x")
        if os.path.exists(z3x_appdata):
            shutil.copytree(z3x_appdata, f"{backup_dir}\\z3x_appdata", dirs_exist_ok=True)
            print("✅ Backup de archivos Z3X creado")
    except:
        print("❌ Error en backup de archivos Z3X")
    
    return backup_dir

def generate_new_mac():
    """Genera nueva MAC address válida"""
    prefixes = ["00:1B:44", "00:50:B6", "00:E0:4C", "08:00:27", "52:54:00"]
    prefix = random.choice(prefixes)
    suffix = ":".join([f"{random.randint(0, 255):02X}" for _ in range(3)])
    return f"{prefix}:{suffix}"

def change_machine_guid():
    """Cambia el Machine GUID de Windows"""
    try:
        new_guid = str(uuid.uuid4())
        
        with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Microsoft\Cryptography", 
                          0, winreg.KEY_SET_VALUE) as key:
            winreg.SetValueEx(key, "MachineGuid", 0, winreg.REG_SZ, new_guid)
        
        print(f"✅ Machine GUID cambiado a: {new_guid}")
        return new_guid
    except Exception as e:
        print(f"❌ Error cambiando Machine GUID: {e}")
        return None

def change_mac_address():
    """Cambia la MAC address del adaptador principal"""
    try:
        new_mac = generate_new_mac()
        
        # Obtener adaptadores de red
        result = subprocess.run(['getmac', '/fo', 'csv', '/nh'], 
                              capture_output=True, text=True, shell=True)
        
        if result.stdout:
            lines = result.stdout.strip().split('\n')
            if lines:
                # Tomar el primer adaptador válido
                for line in lines:
                    if ',' in line:
                        parts = line.split(',')
                        if len(parts) >= 2:
                            current_mac = parts[0].strip('"')
                            adapter_name = parts[1].strip('"')
                            
                            if current_mac and current_mac != 'N/A':
                                # Intentar cambiar MAC via registro
                                try:
                                    reg_path = r"SYSTEM\CurrentControlSet\Control\Class\{4d36e972-e325-11ce-bfc1-08002be10318}"
                                    
                                    with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, reg_path) as key:
                                        i = 0
                                        while True:
                                            try:
                                                subkey_name = winreg.EnumKey(key, i)
                                                subkey_path = f"{reg_path}\\{subkey_name}"
                                                
                                                with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, subkey_path, 
                                                                  0, winreg.KEY_SET_VALUE | winreg.KEY_QUERY_VALUE) as subkey:
                                                    try:
                                                        # Cambiar MAC
                                                        mac_clean = new_mac.replace(":", "").replace("-", "")
                                                        winreg.SetValueEx(subkey, "NetworkAddress", 0, winreg.REG_SZ, mac_clean)
                                                        print(f"✅ MAC cambiada a: {new_mac}")
                                                        return new_mac
                                                    except:
                                                        pass
                                                i += 1
                                            except WindowsError:
                                                break
                                except:
                                    pass
                                break
        
        print(f"⚠️ No se pudo cambiar MAC automáticamente")
        return None
        
    except Exception as e:
        print(f"❌ Error cambiando MAC: {e}")
        return None

def clean_z3x_registry():
    """Limpia entradas Z3X del registro"""
    try:
        print("🧹 Limpiando registro Z3X...")
        
        # Claves a eliminar
        keys_to_delete = [
            (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Z3X"),
            (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\WOW6432Node\Z3X"),
            (winreg.HKEY_CURRENT_USER, r"SOFTWARE\Z3X")
        ]
        
        for hive, key_path in keys_to_delete:
            try:
                winreg.DeleteKey(hive, key_path)
                print(f"✅ Eliminada: {key_path}")
            except FileNotFoundError:
                pass
            except Exception as e:
                print(f"⚠️ No se pudo eliminar {key_path}: {e}")
        
        return True
    except Exception as e:
        print(f"❌ Error limpiando registro: {e}")
        return False

def clean_z3x_files():
    """Limpia archivos de configuración Z3X"""
    try:
        print("🧹 Limpiando archivos Z3X...")
        
        # Eliminar directorio de configuración
        z3x_appdata = os.path.expanduser(r"~\AppData\Roaming\z3x")
        if os.path.exists(z3x_appdata):
            shutil.rmtree(z3x_appdata)
            print(f"✅ Eliminado: {z3x_appdata}")
        
        # Limpiar archivos temporales
        temp_dirs = [
            os.environ.get('TEMP', ''),
            os.path.expanduser(r"~\AppData\Local\Temp")
        ]
        
        for temp_dir in temp_dirs:
            if os.path.exists(temp_dir):
                for root, dirs, files in os.walk(temp_dir):
                    for file in files:
                        if any(term in file.lower() for term in ['z3x', 'samstool']):
                            try:
                                file_path = os.path.join(root, file)
                                os.remove(file_path)
                                print(f"✅ Eliminado archivo temporal: {file}")
                            except:
                                pass
        
        return True
    except Exception as e:
        print(f"❌ Error limpiando archivos: {e}")
        return False

def modify_system_uuid():
    """Intenta modificar el System UUID"""
    try:
        # Generar nuevo UUID
        new_uuid = str(uuid.uuid4()).upper()
        
        # Intentar modificar via WMI (requiere herramientas especiales)
        print(f"⚠️ System UUID requiere herramientas especiales para cambiar")
        print(f"💡 Nuevo UUID sugerido: {new_uuid}")
        
        return new_uuid
    except Exception as e:
        print(f"❌ Error con System UUID: {e}")
        return None

def create_vm_script():
    """Crea script para configurar máquina virtual"""
    vm_script = """
@echo off
echo ================================================================
echo CONFIGURACION DE MAQUINA VIRTUAL PARA Z3X
echo ================================================================
echo.
echo PASOS PARA CREAR VM:
echo 1. Descargar VirtualBox: https://www.virtualbox.org/
echo 2. Crear nueva VM con Windows 10/11
echo 3. Configurar:
echo    - RAM: 4GB minimo
echo    - Disco: 50GB minimo
echo    - Red: NAT
echo 4. Instalar Windows en la VM
echo 5. Instalar Z3X Samstool en la VM
echo 6. Usar cuenta desde la VM
echo.
echo VENTAJAS:
echo - Hardware completamente diferente
echo - Facil de resetear
echo - Aislamiento total
echo.
echo ================================================================
pause
"""
    
    with open("crear_vm_z3x.bat", "w", encoding="utf-8") as f:
        f.write(vm_script)
    
    print("✅ Script de VM creado: crear_vm_z3x.bat")

def generate_recovery_report(backup_dir, changes):
    """Genera reporte de recuperación"""
    report = {
        "timestamp": datetime.now().isoformat(),
        "backup_directory": backup_dir,
        "changes_made": changes,
        "original_identifiers": {
            "machine_guid": "42006B9C-19E1-0000-0000-000000000000",
            "primary_mac": "Detectada automáticamente",
            "cpu_id": "AMD64 Family 25 Model 80 Stepping 0",
            "disk_serial": "0000_0006_2212_1378_CAF2_5B04_1000_1713",
            "system_uuid": "42006B9C-19E1-0000-0000-000000000000"
        },
        "new_identifiers": changes,
        "next_steps": [
            "1. REINICIAR el sistema completamente",
            "2. Abrir Z3X Samstool",
            "3. Intentar iniciar sesión con tu cuenta",
            "4. Si no funciona, usar máquina virtual",
            "5. Contactar soporte Z3X si es necesario"
        ],
        "support_info": {
            "message": "Cuenta recuperada después de compromiso de seguridad",
            "new_hardware_info": "Proporcionar nuevos identificadores a soporte Z3X"
        }
    }
    
    report_file = f"REPORTE_RECUPERACION_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(report_file, "w", encoding="utf-8") as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print(f"📊 Reporte guardado: {report_file}")
    return report_file

def main():
    print("="*70)
    print("🔓 RECUPERACIÓN AUTOMÁTICA DE CUENTA Z3X SAMSTOOL")
    print("="*70)
    print("Esta herramienta modificará automáticamente los identificadores")
    print("de hardware que Z3X usa para reconocer tu PC, permitiéndote")
    print("recuperar el acceso a tu cuenta robada.")
    print("="*70)
    
    # Verificar privilegios
    if not check_admin():
        print("❌ ERROR: Se requieren privilegios de Administrador")
        print("Ejecuta este script como Administrador")
        input("Presiona Enter para salir...")
        return
    
    print("✅ Ejecutándose como Administrador")
    
    # Crear backup
    backup_dir = create_backup()
    
    print(f"\n{'='*50}")
    print("🔄 INICIANDO MODIFICACIONES DE HARDWARE")
    print("="*50)
    
    changes_made = {}
    
    # 1. Cambiar Machine GUID
    print("\n1️⃣ Modificando Machine GUID...")
    new_guid = change_machine_guid()
    if new_guid:
        changes_made["machine_guid"] = new_guid
    
    # 2. Cambiar MAC Address
    print("\n2️⃣ Modificando MAC Address...")
    new_mac = change_mac_address()
    if new_mac:
        changes_made["mac_address"] = new_mac
    
    # 3. Limpiar registro Z3X
    print("\n3️⃣ Limpiando registro Z3X...")
    if clean_z3x_registry():
        changes_made["registry_cleaned"] = True
    
    # 4. Limpiar archivos Z3X
    print("\n4️⃣ Limpiando archivos Z3X...")
    if clean_z3x_files():
        changes_made["files_cleaned"] = True
    
    # 5. Información sobre System UUID
    print("\n5️⃣ Información sobre System UUID...")
    new_uuid = modify_system_uuid()
    if new_uuid:
        changes_made["suggested_uuid"] = new_uuid
    
    # 6. Crear script de VM
    print("\n6️⃣ Creando script de máquina virtual...")
    create_vm_script()
    changes_made["vm_script_created"] = True
    
    # Generar reporte
    print(f"\n{'='*50}")
    print("📊 GENERANDO REPORTE DE RECUPERACIÓN")
    print("="*50)
    
    report_file = generate_recovery_report(backup_dir, changes_made)
    
    # Mostrar resumen
    print(f"\n{'='*70}")
    print("✅ RECUPERACIÓN COMPLETADA")
    print("="*70)
    
    print(f"📁 Backup creado en: {backup_dir}")
    print(f"📊 Reporte generado: {report_file}")
    
    print(f"\n🔄 CAMBIOS REALIZADOS:")
    for change, value in changes_made.items():
        if change == "machine_guid":
            print(f"  ✅ Machine GUID: {value}")
        elif change == "mac_address":
            print(f"  ✅ MAC Address: {value}")
        elif change == "registry_cleaned":
            print(f"  ✅ Registro Z3X limpiado")
        elif change == "files_cleaned":
            print(f"  ✅ Archivos Z3X eliminados")
        elif change == "vm_script_created":
            print(f"  ✅ Script de VM creado")
    
    print(f"\n🚀 PRÓXIMOS PASOS:")
    print("1. 🔄 REINICIA tu computadora AHORA")
    print("2. 🔓 Abre Z3X Samstool")
    print("3. 🔑 Intenta iniciar sesión con tu cuenta")
    print("4. 💻 Si no funciona, usa la máquina virtual")
    print("5. 📞 Contacta soporte Z3X si es necesario")
    
    print(f"\n⚠️ IMPORTANTE:")
    print("- El sistema debe reiniciarse para aplicar cambios")
    print("- Z3X verá tu PC como un equipo completamente nuevo")
    print("- Tienes backup completo en caso de problemas")
    
    print("="*70)
    
    restart = input("\n¿Reiniciar el sistema AHORA? (s/n): ")
    if restart.lower() == 's':
        print("🔄 Reiniciando sistema...")
        subprocess.run("shutdown /r /t 10", shell=True)
        print("💻 El sistema se reiniciará en 10 segundos...")
    else:
        print("⚠️ Recuerda reiniciar manualmente para aplicar cambios")
    
    input("\nPresiona Enter para finalizar...")

if __name__ == "__main__":
    main()
