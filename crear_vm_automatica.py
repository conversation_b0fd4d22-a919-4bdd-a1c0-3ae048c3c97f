#!/usr/bin/env python3
"""
CREAR VM AUTOMÁTICA PARA Z3X - Descarga e instala VirtualBox automáticamente
"""

import os
import sys
import subprocess
import urllib.request
import json
from datetime import datetime

def download_file(url, filename):
    """Descarga un archivo con barra de progreso"""
    def progress_hook(block_num, block_size, total_size):
        downloaded = block_num * block_size
        if total_size > 0:
            percent = min(100, (downloaded * 100) // total_size)
            print(f"\r📥 Descargando {filename}: {percent}%", end="", flush=True)
    
    try:
        urllib.request.urlretrieve(url, filename, progress_hook)
        print(f"\n✅ {filename} descargado exitosamente")
        return True
    except Exception as e:
        print(f"\n❌ Error descargando {filename}: {e}")
        return False

def check_virtualbox_installed():
    """Verifica si VirtualBox está instalado"""
    try:
        result = subprocess.run(['VBoxManage', '--version'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            version = result.stdout.strip()
            print(f"✅ VirtualBox ya está instalado: {version}")
            return True
    except:
        pass
    
    print("❌ VirtualBox no está instalado")
    return False

def download_and_install_virtualbox():
    """Descarga e instala VirtualBox automáticamente"""
    print("📥 Descargando VirtualBox...")
    
    # URL de VirtualBox para Windows
    vbox_url = "https://download.virtualbox.org/virtualbox/7.0.12/VirtualBox-7.0.12-159484-Win.exe"
    vbox_installer = "VirtualBox-Installer.exe"
    
    if download_file(vbox_url, vbox_installer):
        print("🔧 Instalando VirtualBox...")
        try:
            # Instalar VirtualBox silenciosamente
            result = subprocess.run([vbox_installer, '--silent'], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ VirtualBox instalado exitosamente")
                # Limpiar instalador
                os.remove(vbox_installer)
                return True
            else:
                print(f"❌ Error instalando VirtualBox: {result.stderr}")
                return False
        except Exception as e:
            print(f"❌ Error ejecutando instalador: {e}")
            return False
    
    return False

def download_windows_iso():
    """Proporciona instrucciones para descargar Windows ISO"""
    print("\n💿 DESCARGA DE WINDOWS ISO")
    print("="*50)
    print("Para crear la VM necesitas una ISO de Windows 10/11.")
    print()
    print("🔗 ENLACES OFICIALES:")
    print("Windows 11: https://www.microsoft.com/software-download/windows11")
    print("Windows 10: https://www.microsoft.com/software-download/windows10")
    print()
    print("📋 PASOS:")
    print("1. Ve a uno de los enlaces anteriores")
    print("2. Selecciona 'Descargar ahora la herramienta'")
    print("3. Ejecuta la herramienta")
    print("4. Selecciona 'Crear medios de instalación'")
    print("5. Elige 'Archivo ISO'")
    print("6. Guarda la ISO en tu carpeta de Descargas")
    print()
    
    iso_path = input("📁 Ingresa la ruta completa de tu ISO de Windows (o Enter para continuar sin ISO): ")
    
    if iso_path and os.path.exists(iso_path):
        print(f"✅ ISO encontrada: {iso_path}")
        return iso_path
    else:
        print("⚠️ Continuando sin ISO. Podrás agregarla después.")
        return None

def create_vm_automatically(iso_path=None):
    """Crea la máquina virtual automáticamente"""
    vm_name = "Z3X-Recovery-VM"
    
    print(f"\n🔧 CREANDO MÁQUINA VIRTUAL: {vm_name}")
    print("="*50)
    
    try:
        # Crear VM
        print("1️⃣ Creando máquina virtual...")
        cmd = [
            'VBoxManage', 'createvm',
            '--name', vm_name,
            '--ostype', 'Windows10_64',
            '--register'
        ]
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode != 0:
            print(f"❌ Error creando VM: {result.stderr}")
            return False
        print("✅ Máquina virtual creada")
        
        # Configurar memoria
        print("2️⃣ Configurando memoria (4GB)...")
        cmd = ['VBoxManage', 'modifyvm', vm_name, '--memory', '4096']
        subprocess.run(cmd, check=True)
        print("✅ Memoria configurada")
        
        # Configurar CPUs
        print("3️⃣ Configurando CPUs (2 cores)...")
        cmd = ['VBoxManage', 'modifyvm', vm_name, '--cpus', '2']
        subprocess.run(cmd, check=True)
        print("✅ CPUs configuradas")
        
        # Crear disco duro
        print("4️⃣ Creando disco duro virtual (50GB)...")
        vdi_path = f"{vm_name}.vdi"
        cmd = [
            'VBoxManage', 'createhd',
            '--filename', vdi_path,
            '--size', '51200',  # 50GB en MB
            '--format', 'VDI'
        ]
        subprocess.run(cmd, check=True)
        print("✅ Disco duro creado")
        
        # Agregar controlador SATA
        print("5️⃣ Configurando almacenamiento...")
        cmd = ['VBoxManage', 'storagectl', vm_name, '--name', 'SATA', '--add', 'sata']
        subprocess.run(cmd, check=True)
        
        # Conectar disco duro
        cmd = [
            'VBoxManage', 'storageattach', vm_name,
            '--storagectl', 'SATA',
            '--port', '0',
            '--device', '0',
            '--type', 'hdd',
            '--medium', vdi_path
        ]
        subprocess.run(cmd, check=True)
        print("✅ Almacenamiento configurado")
        
        # Configurar red con MAC única
        print("6️⃣ Configurando red con MAC única...")
        cmd = [
            'VBoxManage', 'modifyvm', vm_name,
            '--nic1', 'nat',
            '--macaddress1', 'auto'
        ]
        subprocess.run(cmd, check=True)
        print("✅ Red configurada con MAC única")
        
        # Configurar ISO si está disponible
        if iso_path and os.path.exists(iso_path):
            print("7️⃣ Configurando ISO de Windows...")
            # Agregar controlador IDE para DVD
            cmd = ['VBoxManage', 'storagectl', vm_name, '--name', 'IDE', '--add', 'ide']
            subprocess.run(cmd, check=True)
            
            # Conectar ISO
            cmd = [
                'VBoxManage', 'storageattach', vm_name,
                '--storagectl', 'IDE',
                '--port', '0',
                '--device', '0',
                '--type', 'dvddrive',
                '--medium', iso_path
            ]
            subprocess.run(cmd, check=True)
            print("✅ ISO de Windows configurada")
        
        # Configuraciones adicionales
        print("8️⃣ Aplicando configuraciones adicionales...")
        additional_configs = [
            ['--vram', '128'],  # Memoria de video
            ['--accelerate3d', 'on'],  # Aceleración 3D
            ['--clipboard', 'bidirectional'],  # Portapapeles compartido
            ['--draganddrop', 'bidirectional'],  # Drag and drop
            ['--boot1', 'dvd'],  # Boot desde DVD primero
            ['--boot2', 'disk'],  # Luego desde disco
        ]
        
        for config in additional_configs:
            cmd = ['VBoxManage', 'modifyvm', vm_name] + config
            subprocess.run(cmd, check=True)
        
        print("✅ Configuraciones adicionales aplicadas")
        
        print(f"\n🎉 ¡MÁQUINA VIRTUAL CREADA EXITOSAMENTE!")
        print("="*50)
        print(f"📛 Nombre: {vm_name}")
        print("💾 RAM: 4GB")
        print("💻 CPUs: 2 cores")
        print("💿 Disco: 50GB")
        print("🌐 Red: NAT con MAC única")
        print("🎮 3D: Habilitado")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Error configurando VM: {e}")
        return False
    except Exception as e:
        print(f"❌ Error inesperado: {e}")
        return False

def start_vm(vm_name="Z3X-Recovery-VM"):
    """Inicia la máquina virtual"""
    try:
        print(f"\n🚀 INICIANDO MÁQUINA VIRTUAL: {vm_name}")
        print("="*50)
        
        cmd = ['VBoxManage', 'startvm', vm_name, '--type', 'gui']
        subprocess.run(cmd, check=True)
        
        print("✅ Máquina virtual iniciada")
        print("\n📋 PRÓXIMOS PASOS EN LA VM:")
        print("1. Instalar Windows (seguir asistente)")
        print("2. Instalar Guest Additions de VirtualBox")
        print("3. Descargar Z3X Samstool dentro de la VM")
        print("4. Instalar Z3X en la VM")
        print("5. Usar tu cuenta Z3X desde la VM")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Error iniciando VM: {e}")
        return False

def create_vm_instructions():
    """Crea archivo con instrucciones detalladas"""
    instructions = f"""
# INSTRUCCIONES PARA USAR LA MÁQUINA VIRTUAL Z3X

## VM Creada: Z3X-Recovery-VM

### ESPECIFICACIONES:
- Sistema: Windows 10/11 (64-bit)
- RAM: 4GB
- CPUs: 2 cores
- Disco: 50GB
- Red: NAT con MAC única
- 3D: Habilitado

### PASOS PARA COMPLETAR LA INSTALACIÓN:

#### 1. INSTALAR WINDOWS:
- La VM debería iniciarse automáticamente
- Seguir el asistente de instalación de Windows
- Crear cuenta de usuario
- Completar configuración inicial

#### 2. INSTALAR GUEST ADDITIONS:
- En la VM: Dispositivos → Insertar imagen de CD de Guest Additions
- Ejecutar VBoxWindowsAdditions.exe
- Reiniciar la VM
- Esto mejorará el rendimiento y permitirá pantalla completa

#### 3. CONFIGURAR RED:
- Verificar que tienes conexión a internet en la VM
- Si no hay internet, cambiar red a "Bridge" en configuración

#### 4. INSTALAR Z3X SAMSTOOL:
- Dentro de la VM, abrir navegador
- Ir al sitio oficial de Z3X
- Descargar SamsTool
- Instalar normalmente

#### 5. USAR TU CUENTA:
- Abrir Z3X SamsTool en la VM
- Intentar login con tu cuenta
- Debería funcionar porque la VM tiene hardware completamente diferente

### COMANDOS ÚTILES DE VIRTUALBOX:

#### Iniciar VM:
VBoxManage startvm Z3X-Recovery-VM --type gui

#### Detener VM:
VBoxManage controlvm Z3X-Recovery-VM poweroff

#### Ver información de VM:
VBoxManage showvminfo Z3X-Recovery-VM

#### Crear snapshot:
VBoxManage snapshot Z3X-Recovery-VM take "Windows-Limpio"

#### Restaurar snapshot:
VBoxManage snapshot Z3X-Recovery-VM restore "Windows-Limpio"

### SOLUCIÓN DE PROBLEMAS:

#### Si la VM es muy lenta:
1. Aumentar RAM a 6-8GB
2. Aumentar CPUs a 4 cores
3. Habilitar VT-x/AMD-V en BIOS
4. Cerrar programas innecesarios en el host

#### Si Z3X no funciona:
1. Verificar conexión a internet en VM
2. Desactivar antivirus temporalmente
3. Ejecutar Z3X como administrador
4. Verificar que Windows esté activado

#### Si no hay conexión a internet:
1. Cambiar red de NAT a Bridge
2. Reiniciar VM
3. Verificar configuración de red en Windows

### BACKUP Y SNAPSHOTS:

#### Crear snapshot antes de instalar Z3X:
1. Apagar la VM completamente
2. VBoxManage snapshot Z3X-Recovery-VM take "Antes-Z3X"
3. Si algo sale mal, restaurar este snapshot

#### Crear snapshot después de instalar Z3X:
1. VBoxManage snapshot Z3X-Recovery-VM take "Z3X-Funcionando"
2. Usar este como punto de restauración

### VENTAJAS DE ESTA SOLUCIÓN:

✅ Hardware completamente diferente al sistema principal
✅ Z3X verá la VM como un PC totalmente distinto
✅ Fácil de resetear si hay problemas
✅ Aislamiento total del sistema principal
✅ Múltiples snapshots para diferentes estados
✅ No afecta el sistema principal

### IMPORTANTE:

⚠️ Necesitas una licencia válida de Windows para la VM
⚠️ La VM será más lenta que el sistema nativo
⚠️ Asegúrate de tener suficiente espacio en disco (al menos 60GB libres)
⚠️ Instala Guest Additions para mejor rendimiento

¡Esta es la solución MÁS EFECTIVA para recuperar tu cuenta Z3X!

Fecha de creación: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
    
    with open("INSTRUCCIONES_VM_Z3X.txt", "w", encoding="utf-8") as f:
        f.write(instructions)
    
    print("📋 Instrucciones detalladas creadas: INSTRUCCIONES_VM_Z3X.txt")

def main():
    print("="*70)
    print("🚀 CREADOR AUTOMÁTICO DE VM PARA Z3X")
    print("="*70)
    print("Esta herramienta descargará e instalará VirtualBox automáticamente")
    print("y creará una máquina virtual optimizada para recuperar tu cuenta Z3X.")
    print("="*70)
    
    # Verificar si VirtualBox está instalado
    if not check_virtualbox_installed():
        print("\n📥 VirtualBox no está instalado. Descargando e instalando...")
        
        confirm = input("¿Descargar e instalar VirtualBox automáticamente? (s/n): ")
        if confirm.lower() != 's':
            print("❌ Instalación cancelada")
            return
        
        if not download_and_install_virtualbox():
            print("❌ Error instalando VirtualBox")
            print("💡 Puedes descargarlo manualmente desde: https://www.virtualbox.org/")
            return
    
    # Descargar Windows ISO (opcional)
    iso_path = download_windows_iso()
    
    # Crear VM automáticamente
    if create_vm_automatically(iso_path):
        # Crear instrucciones
        create_vm_instructions()
        
        print(f"\n{'='*70}")
        print("✅ MÁQUINA VIRTUAL LISTA PARA USAR")
        print("="*70)
        
        start_choice = input("¿Iniciar la máquina virtual ahora? (s/n): ")
        if start_choice.lower() == 's':
            start_vm()
        else:
            print("💡 Puedes iniciar la VM después desde VirtualBox")
            print("📋 Lee INSTRUCCIONES_VM_Z3X.txt para más detalles")
        
        print(f"\n🎯 RESULTADO:")
        print("✅ VirtualBox instalado")
        print("✅ VM 'Z3X-Recovery-VM' creada")
        print("✅ Hardware único configurado")
        print("✅ Instrucciones detalladas creadas")
        
        print(f"\n🚀 PRÓXIMOS PASOS:")
        print("1. Instalar Windows en la VM")
        print("2. Instalar Guest Additions")
        print("3. Descargar Z3X en la VM")
        print("4. Usar tu cuenta Z3X desde la VM")
        
        print(f"\n💡 ¡Tu cuenta Z3X debería funcionar perfectamente en la VM!")
        
    else:
        print("❌ Error creando la máquina virtual")
        print("💡 Puedes crear la VM manualmente siguiendo las instrucciones")

if __name__ == "__main__":
    main()
