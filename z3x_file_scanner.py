#!/usr/bin/env python3
"""
Z3X File Scanner - Busca automáticamente todos los archivos relacionados con Z3X Samstool
"""

import os
import glob
import json
import hashlib
import time
from datetime import datetime
from pathlib import Path
import logging
import shutil

logger = logging.getLogger(__name__)

class Z3XFileScanner:
    def __init__(self):
        self.found_files = []
        self.z3x_directories = []
        self.config_files = []
        self.license_files = []
        self.log_files = []
        self.backup_dir = f"z3x_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
    def get_search_locations(self):
        """Obtiene todas las ubicaciones donde Z3X podría estar instalado"""
        locations = []
        
        # Ubicaciones estándar de programas
        program_dirs = [
            "C:\\Program Files",
            "C:\\Program Files (x86)",
            "C:\\ProgramData"
        ]
        
        # Directorios de usuario
        user_dirs = [
            os.path.expanduser("~"),
            os.path.join(os.path.expanduser("~"), "AppData", "Local"),
            os.path.join(os.path.expanduser("~"), "AppData", "Roaming"),
            os.path.join(os.path.expanduser("~"), "AppData", "LocalLow"),
            os.path.join(os.path.expanduser("~"), "Documents"),
            os.path.join(os.path.expanduser("~"), "Desktop")
        ]
        
        # Directorios del sistema
        system_dirs = [
            "C:\\Windows\\System32",
            "C:\\Windows\\SysWOW64",
            "C:\\Windows\\Temp",
            os.environ.get('TEMP', 'C:\\Temp')
        ]
        
        locations.extend(program_dirs)
        locations.extend(user_dirs)
        locations.extend(system_dirs)
        
        # Agregar todas las unidades disponibles
        for drive in "ABCDEFGHIJKLMNOPQRSTUVWXYZ":
            drive_path = f"{drive}:\\"
            if os.path.exists(drive_path):
                locations.append(drive_path)
        
        return locations
    
    def get_search_patterns(self):
        """Patrones de búsqueda para archivos relacionados con Z3X"""
        return [
            "*z3x*",
            "*Z3X*",
            "*sam*tool*",
            "*Sam*Tool*",
            "*SAMSTOOL*",
            "*samsung*tool*",
            "*Samsung*Tool*",
            "*.z3x",
            "*.lic",
            "*.license",
            "*hwid*",
            "*HWID*",
            "*dongle*",
            "*Dongle*",
            "*box*id*",
            "*BoxID*"
        ]
    
    def scan_directory(self, directory, max_depth=3, current_depth=0):
        """Escanea un directorio buscando archivos relacionados con Z3X"""
        if current_depth > max_depth:
            return
            
        try:
            if not os.path.exists(directory):
                return
                
            print(f"Escaneando: {directory}")
            
            patterns = self.get_search_patterns()
            
            for pattern in patterns:
                try:
                    # Buscar archivos con el patrón
                    search_path = os.path.join(directory, "**", pattern)
                    files = glob.glob(search_path, recursive=True)
                    
                    for file_path in files:
                        if os.path.isfile(file_path):
                            self.analyze_file(file_path)
                        elif os.path.isdir(file_path):
                            self.z3x_directories.append(file_path)
                            # Escanear subdirectorios Z3X más profundamente
                            self.scan_directory(file_path, max_depth=5, current_depth=0)
                            
                except Exception as e:
                    continue
                    
        except PermissionError:
            logger.warning(f"Sin permisos para acceder a: {directory}")
        except Exception as e:
            logger.error(f"Error escaneando {directory}: {e}")
    
    def analyze_file(self, file_path):
        """Analiza un archivo encontrado"""
        try:
            file_info = {
                'path': file_path,
                'name': os.path.basename(file_path),
                'size': os.path.getsize(file_path),
                'modified': datetime.fromtimestamp(os.path.getmtime(file_path)).isoformat(),
                'created': datetime.fromtimestamp(os.path.getctime(file_path)).isoformat(),
                'extension': os.path.splitext(file_path)[1].lower(),
                'type': 'unknown'
            }
            
            # Calcular hash del archivo
            try:
                with open(file_path, 'rb') as f:
                    file_hash = hashlib.md5(f.read()).hexdigest()
                    file_info['md5'] = file_hash
            except:
                file_info['md5'] = 'error'
            
            # Clasificar tipo de archivo
            file_name_lower = file_info['name'].lower()
            
            if any(ext in file_info['extension'] for ext in ['.ini', '.cfg', '.conf', '.config']):
                file_info['type'] = 'config'
                self.config_files.append(file_info)
            elif any(ext in file_info['extension'] for ext in ['.lic', '.license', '.key']):
                file_info['type'] = 'license'
                self.license_files.append(file_info)
            elif any(ext in file_info['extension'] for ext in ['.log', '.txt']):
                file_info['type'] = 'log'
                self.log_files.append(file_info)
            elif file_info['extension'] in ['.exe', '.dll']:
                file_info['type'] = 'executable'
            elif file_info['extension'] in ['.dat', '.bin']:
                file_info['type'] = 'data'
            
            self.found_files.append(file_info)
            
            print(f"Encontrado: {file_path} ({file_info['type']})")
            
        except Exception as e:
            logger.error(f"Error analizando archivo {file_path}: {e}")
    
    def create_backup(self):
        """Crea backup de archivos importantes encontrados"""
        if not os.path.exists(self.backup_dir):
            os.makedirs(self.backup_dir)
        
        important_files = self.config_files + self.license_files
        
        for file_info in important_files:
            try:
                source_path = file_info['path']
                backup_path = os.path.join(self.backup_dir, file_info['name'])
                
                # Si ya existe, agregar número
                counter = 1
                while os.path.exists(backup_path):
                    name, ext = os.path.splitext(file_info['name'])
                    backup_path = os.path.join(self.backup_dir, f"{name}_{counter}{ext}")
                    counter += 1
                
                shutil.copy2(source_path, backup_path)
                print(f"Backup creado: {backup_path}")
                
            except Exception as e:
                logger.error(f"Error creando backup de {file_info['path']}: {e}")
    
    def read_text_files(self):
        """Lee archivos de texto para buscar información relevante"""
        text_content = {}
        
        readable_files = [f for f in self.found_files 
                         if f['extension'] in ['.txt', '.log', '.ini', '.cfg', '.conf']]
        
        for file_info in readable_files:
            try:
                with open(file_info['path'], 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                    text_content[file_info['path']] = content
                    
                    # Buscar información relevante
                    if any(keyword in content.lower() for keyword in 
                           ['hwid', 'hardware', 'serial', 'mac', 'guid', 'license']):
                        print(f"Información relevante encontrada en: {file_info['path']}")
                        
            except Exception as e:
                logger.error(f"Error leyendo {file_info['path']}: {e}")
        
        return text_content
    
    def scan_system(self):
        """Escanea todo el sistema buscando archivos Z3X"""
        print("="*60)
        print("INICIANDO ESCANEO COMPLETO DEL SISTEMA")
        print("Buscando archivos relacionados con Z3X Samstool...")
        print("="*60)
        
        start_time = time.time()
        
        # Obtener ubicaciones de búsqueda
        locations = self.get_search_locations()
        
        print(f"Escaneando {len(locations)} ubicaciones...")
        
        # Escanear cada ubicación
        for location in locations:
            self.scan_directory(location)
        
        # Crear backup de archivos importantes
        if self.config_files or self.license_files:
            print(f"\nCreando backup de archivos importantes...")
            self.create_backup()
        
        # Leer archivos de texto
        print(f"\nLeyendo archivos de configuración...")
        text_content = self.read_text_files()
        
        end_time = time.time()
        scan_duration = end_time - start_time
        
        # Generar reporte
        self.generate_report(text_content, scan_duration)
        
        return self.found_files
    
    def generate_report(self, text_content, scan_duration):
        """Genera reporte detallado del escaneo"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"z3x_scan_report_{timestamp}.json"
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'scan_duration_seconds': scan_duration,
            'summary': {
                'total_files_found': len(self.found_files),
                'config_files': len(self.config_files),
                'license_files': len(self.license_files),
                'log_files': len(self.log_files),
                'directories_found': len(self.z3x_directories)
            },
            'found_files': self.found_files,
            'config_files': self.config_files,
            'license_files': self.license_files,
            'log_files': self.log_files,
            'z3x_directories': self.z3x_directories,
            'text_content': text_content,
            'backup_directory': self.backup_dir
        }
        
        # Guardar reporte
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        # Mostrar resumen
        print("\n" + "="*60)
        print("RESUMEN DEL ESCANEO")
        print("="*60)
        print(f"Tiempo de escaneo: {scan_duration:.2f} segundos")
        print(f"Archivos encontrados: {len(self.found_files)}")
        print(f"Archivos de configuración: {len(self.config_files)}")
        print(f"Archivos de licencia: {len(self.license_files)}")
        print(f"Archivos de log: {len(self.log_files)}")
        print(f"Directorios Z3X: {len(self.z3x_directories)}")
        print(f"Backup creado en: {self.backup_dir}")
        print(f"Reporte guardado: {report_file}")
        
        # Mostrar archivos más importantes
        if self.license_files:
            print(f"\n🔑 ARCHIVOS DE LICENCIA ENCONTRADOS:")
            for file_info in self.license_files:
                print(f"  - {file_info['path']}")
        
        if self.config_files:
            print(f"\n⚙️ ARCHIVOS DE CONFIGURACIÓN ENCONTRADOS:")
            for file_info in self.config_files[:5]:  # Mostrar solo los primeros 5
                print(f"  - {file_info['path']}")
        
        if self.z3x_directories:
            print(f"\n📁 DIRECTORIOS Z3X ENCONTRADOS:")
            for directory in self.z3x_directories[:5]:  # Mostrar solo los primeros 5
                print(f"  - {directory}")
        
        print("="*60)

if __name__ == "__main__":
    scanner = Z3XFileScanner()
    
    print("Z3X File Scanner")
    print("================")
    print("Este escáner buscará TODOS los archivos relacionados con Z3X Samstool")
    print("en tu sistema para ayudarte a recuperar el acceso a tu cuenta.")
    print("\nEsto puede tomar varios minutos...")
    print("\nPresiona Enter para comenzar...")
    input()
    
    try:
        found_files = scanner.scan_system()
        
        if found_files:
            print(f"\n✅ Escaneo completado. Se encontraron {len(found_files)} archivos.")
            print("Revisa el reporte generado para más detalles.")
        else:
            print("\n❌ No se encontraron archivos relacionados con Z3X.")
            print("Verifica que Z3X Samstool esté instalado en este sistema.")
            
    except KeyboardInterrupt:
        print("\n\nEscaneo interrumpido por el usuario.")
    except Exception as e:
        print(f"\nError durante el escaneo: {e}")
        logger.error(f"Error en escaneo: {e}")
