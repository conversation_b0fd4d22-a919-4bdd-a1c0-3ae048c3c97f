#!/usr/bin/env python3
"""
Hardware Fingerprinting Detection Module
Identifies what hardware and system information Z3X Samstool might be collecting
"""

import platform
import uuid
import hashlib
import subprocess
import winreg
import psutil
import wmi
import json
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional

logger = logging.getLogger(__name__)

class HardwareFingerprintDetector:
    def __init__(self):
        self.fingerprint_data = {}
        self.wmi_connection = None
        
        try:
            self.wmi_connection = wmi.WMI()
        except Exception as e:
            logger.warning(f"Could not initialize WMI connection: {e}")
    
    def get_cpu_info(self) -> Dict[str, Any]:
        """Get detailed CPU information"""
        cpu_info = {
            'processor': platform.processor(),
            'architecture': platform.architecture(),
            'machine': platform.machine(),
            'cpu_count': psutil.cpu_count(),
            'cpu_count_logical': psutil.cpu_count(logical=True),
            'cpu_freq': psutil.cpu_freq()._asdict() if psutil.cpu_freq() else None
        }
        
        if self.wmi_connection:
            try:
                for processor in self.wmi_connection.Win32_Processor():
                    cpu_info.update({
                        'name': processor.Name,
                        'manufacturer': processor.Manufacturer,
                        'family': processor.Family,
                        'model': processor.Model,
                        'stepping': processor.Stepping,
                        'processor_id': processor.ProcessorId,
                        'serial_number': processor.SerialNumber,
                        'socket_designation': processor.SocketDesignation,
                        'max_clock_speed': processor.MaxClockSpeed,
                        'current_clock_speed': processor.CurrentClockSpeed,
                        'l2_cache_size': processor.L2CacheSize,
                        'l3_cache_size': processor.L3CacheSize
                    })
                    break  # Usually just need the first processor
            except Exception as e:
                logger.error(f"Error getting WMI CPU info: {e}")
        
        return cpu_info
    
    def get_memory_info(self) -> Dict[str, Any]:
        """Get memory information"""
        memory_info = {
            'total': psutil.virtual_memory().total,
            'available': psutil.virtual_memory().available,
            'percent': psutil.virtual_memory().percent
        }
        
        if self.wmi_connection:
            try:
                memory_modules = []
                for memory in self.wmi_connection.Win32_PhysicalMemory():
                    memory_modules.append({
                        'capacity': memory.Capacity,
                        'speed': memory.Speed,
                        'manufacturer': memory.Manufacturer,
                        'part_number': memory.PartNumber,
                        'serial_number': memory.SerialNumber,
                        'device_locator': memory.DeviceLocator,
                        'memory_type': memory.MemoryType,
                        'form_factor': memory.FormFactor
                    })
                memory_info['modules'] = memory_modules
            except Exception as e:
                logger.error(f"Error getting WMI memory info: {e}")
        
        return memory_info
    
    def get_disk_info(self) -> Dict[str, Any]:
        """Get disk drive information including serial numbers"""
        disk_info = {
            'partitions': [],
            'physical_drives': []
        }
        
        # Get partition info
        for partition in psutil.disk_partitions():
            try:
                usage = psutil.disk_usage(partition.mountpoint)
                disk_info['partitions'].append({
                    'device': partition.device,
                    'mountpoint': partition.mountpoint,
                    'fstype': partition.fstype,
                    'total': usage.total,
                    'used': usage.used,
                    'free': usage.free
                })
            except PermissionError:
                continue
        
        # Get physical drive info via WMI
        if self.wmi_connection:
            try:
                for disk in self.wmi_connection.Win32_DiskDrive():
                    disk_info['physical_drives'].append({
                        'model': disk.Model,
                        'serial_number': disk.SerialNumber,
                        'size': disk.Size,
                        'interface_type': disk.InterfaceType,
                        'media_type': disk.MediaType,
                        'manufacturer': disk.Manufacturer,
                        'firmware_revision': disk.FirmwareRevision,
                        'device_id': disk.DeviceID
                    })
            except Exception as e:
                logger.error(f"Error getting WMI disk info: {e}")
        
        return disk_info
    
    def get_network_info(self) -> Dict[str, Any]:
        """Get network adapter information including MAC addresses"""
        network_info = {
            'interfaces': [],
            'adapters': []
        }
        
        # Get network interfaces
        for interface_name, addresses in psutil.net_if_addrs().items():
            interface_data = {
                'name': interface_name,
                'addresses': []
            }
            
            for addr in addresses:
                interface_data['addresses'].append({
                    'family': addr.family.name,
                    'address': addr.address,
                    'netmask': addr.netmask,
                    'broadcast': addr.broadcast
                })
            
            network_info['interfaces'].append(interface_data)
        
        # Get network adapter details via WMI
        if self.wmi_connection:
            try:
                for adapter in self.wmi_connection.Win32_NetworkAdapter():
                    if adapter.MACAddress:
                        network_info['adapters'].append({
                            'name': adapter.Name,
                            'mac_address': adapter.MACAddress,
                            'manufacturer': adapter.Manufacturer,
                            'product_name': adapter.ProductName,
                            'device_id': adapter.DeviceID,
                            'pnp_device_id': adapter.PNPDeviceID,
                            'adapter_type': adapter.AdapterType,
                            'service_name': adapter.ServiceName,
                            'net_enabled': adapter.NetEnabled
                        })
            except Exception as e:
                logger.error(f"Error getting WMI network info: {e}")
        
        return network_info
    
    def get_motherboard_info(self) -> Dict[str, Any]:
        """Get motherboard information"""
        motherboard_info = {}
        
        if self.wmi_connection:
            try:
                for board in self.wmi_connection.Win32_BaseBoard():
                    motherboard_info.update({
                        'manufacturer': board.Manufacturer,
                        'product': board.Product,
                        'serial_number': board.SerialNumber,
                        'version': board.Version,
                        'model': board.Model
                    })
                    break
            except Exception as e:
                logger.error(f"Error getting motherboard info: {e}")
        
        return motherboard_info
    
    def get_bios_info(self) -> Dict[str, Any]:
        """Get BIOS information"""
        bios_info = {}
        
        if self.wmi_connection:
            try:
                for bios in self.wmi_connection.Win32_BIOS():
                    bios_info.update({
                        'manufacturer': bios.Manufacturer,
                        'name': bios.Name,
                        'serial_number': bios.SerialNumber,
                        'version': bios.Version,
                        'release_date': bios.ReleaseDate,
                        'smbios_version': bios.SMBIOSBIOSVersion,
                        'smbios_major_version': bios.SMBIOSMajorVersion,
                        'smbios_minor_version': bios.SMBIOSMinorVersion
                    })
                    break
            except Exception as e:
                logger.error(f"Error getting BIOS info: {e}")
        
        return bios_info
    
    def get_system_info(self) -> Dict[str, Any]:
        """Get general system information"""
        system_info = {
            'platform': platform.platform(),
            'system': platform.system(),
            'release': platform.release(),
            'version': platform.version(),
            'node': platform.node(),
            'machine_uuid': str(uuid.uuid1()),
            'boot_time': datetime.fromtimestamp(psutil.boot_time()).isoformat()
        }
        
        if self.wmi_connection:
            try:
                for system in self.wmi_connection.Win32_ComputerSystem():
                    system_info.update({
                        'manufacturer': system.Manufacturer,
                        'model': system.Model,
                        'name': system.Name,
                        'primary_owner_name': system.PrimaryOwnerName,
                        'total_physical_memory': system.TotalPhysicalMemory,
                        'domain': system.Domain,
                        'workgroup': system.Workgroup
                    })
                    break
                    
                for os_info in self.wmi_connection.Win32_OperatingSystem():
                    system_info.update({
                        'os_name': os_info.Name,
                        'os_version': os_info.Version,
                        'os_build_number': os_info.BuildNumber,
                        'os_serial_number': os_info.SerialNumber,
                        'install_date': os_info.InstallDate,
                        'last_boot_up_time': os_info.LastBootUpTime
                    })
                    break
            except Exception as e:
                logger.error(f"Error getting system info: {e}")
        
        return system_info
    
    def get_registry_identifiers(self) -> Dict[str, Any]:
        """Get Windows registry identifiers that might be used for fingerprinting"""
        registry_info = {}
        
        try:
            # Machine GUID
            with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, 
                              r"SOFTWARE\Microsoft\Cryptography") as key:
                machine_guid = winreg.QueryValueEx(key, "MachineGuid")[0]
                registry_info['machine_guid'] = machine_guid
        except Exception as e:
            logger.error(f"Error getting machine GUID: {e}")
        
        try:
            # Windows Product ID
            with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE,
                              r"SOFTWARE\Microsoft\Windows NT\CurrentVersion") as key:
                product_id = winreg.QueryValueEx(key, "ProductId")[0]
                registry_info['product_id'] = product_id
        except Exception as e:
            logger.error(f"Error getting product ID: {e}")
        
        return registry_info
    
    def generate_fingerprint_hash(self, data: Dict[str, Any]) -> str:
        """Generate a hash from the collected fingerprint data"""
        # Create a deterministic string from the data
        fingerprint_string = json.dumps(data, sort_keys=True)
        
        # Generate SHA256 hash
        return hashlib.sha256(fingerprint_string.encode()).hexdigest()
    
    def collect_all_fingerprints(self) -> Dict[str, Any]:
        """Collect all possible hardware fingerprinting data"""
        logger.info("Collecting hardware fingerprint data...")
        
        self.fingerprint_data = {
            'timestamp': datetime.now().isoformat(),
            'cpu': self.get_cpu_info(),
            'memory': self.get_memory_info(),
            'disk': self.get_disk_info(),
            'network': self.get_network_info(),
            'motherboard': self.get_motherboard_info(),
            'bios': self.get_bios_info(),
            'system': self.get_system_info(),
            'registry': self.get_registry_identifiers()
        }
        
        # Generate fingerprint hash
        self.fingerprint_data['fingerprint_hash'] = self.generate_fingerprint_hash(
            self.fingerprint_data
        )
        
        logger.info("Hardware fingerprint collection complete")
        return self.fingerprint_data
    
    def save_fingerprint_data(self, filename: Optional[str] = None):
        """Save fingerprint data to file"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"hardware_fingerprint_{timestamp}.json"
        
        try:
            with open(filename, 'w') as f:
                json.dump(self.fingerprint_data, f, indent=2, default=str)
            
            logger.info(f"Hardware fingerprint data saved to {filename}")
            return filename
            
        except Exception as e:
            logger.error(f"Error saving fingerprint data: {e}")
            return None

if __name__ == "__main__":
    detector = HardwareFingerprintDetector()
    
    print("Hardware Fingerprint Detector")
    print("=============================")
    print("Collecting all possible hardware identifiers that Z3X might use...")
    
    fingerprint_data = detector.collect_all_fingerprints()
    filename = detector.save_fingerprint_data()
    
    print(f"\nFingerprint data collected and saved to: {filename}")
    print(f"Fingerprint hash: {fingerprint_data.get('fingerprint_hash', 'N/A')}")
    
    # Display summary
    print("\nSummary of collected identifiers:")
    print(f"- CPU Serial: {fingerprint_data.get('cpu', {}).get('processor_id', 'N/A')}")
    print(f"- Machine GUID: {fingerprint_data.get('registry', {}).get('machine_guid', 'N/A')}")
    print(f"- BIOS Serial: {fingerprint_data.get('bios', {}).get('serial_number', 'N/A')}")
    
    mac_addresses = []
    for adapter in fingerprint_data.get('network', {}).get('adapters', []):
        if adapter.get('mac_address'):
            mac_addresses.append(adapter['mac_address'])
    
    print(f"- MAC Addresses: {', '.join(mac_addresses) if mac_addresses else 'N/A'}")
