#!/usr/bin/env python3
"""
Real-time Communication Dashboard
Web interface to display intercepted Z3X communications in real-time
"""

from flask import Flask, render_template, jsonify, request
from flask_socketio import SocketIO, emit
import json
import threading
import time
import logging
from datetime import datetime
from typing import Dict, List, Any
import os
import glob

logger = logging.getLogger(__name__)

app = Flask(__name__)
app.config['SECRET_KEY'] = 'z3x_monitor_secret_key'
socketio = SocketIO(app, cors_allowed_origins="*")

class DashboardManager:
    def __init__(self):
        self.active_connections = []
        self.captured_data = []
        self.auth_data = []
        self.fingerprint_data = {}
        self.monitoring = False
        
    def load_existing_data(self):
        """Load existing captured data files"""
        try:
            # Load network data
            network_files = glob.glob("z3x_captured_data_*.json")
            for file in sorted(network_files, reverse=True)[:5]:  # Last 5 files
                try:
                    with open(file, 'r') as f:
                        data = json.load(f)
                        self.captured_data.extend(data)
                except Exception as e:
                    logger.error(f"Error loading {file}: {e}")
            
            # Load auth data
            auth_files = glob.glob("z3x_auth_data_*.json")
            for file in sorted(auth_files, reverse=True)[:5]:  # Last 5 files
                try:
                    with open(file, 'r') as f:
                        data = json.load(f)
                        self.auth_data.extend(data)
                except Exception as e:
                    logger.error(f"Error loading {file}: {e}")
            
            # Load fingerprint data
            fingerprint_files = glob.glob("hardware_fingerprint_*.json")
            if fingerprint_files:
                latest_fingerprint = sorted(fingerprint_files, reverse=True)[0]
                try:
                    with open(latest_fingerprint, 'r') as f:
                        self.fingerprint_data = json.load(f)
                except Exception as e:
                    logger.error(f"Error loading fingerprint data: {e}")
                    
        except Exception as e:
            logger.error(f"Error loading existing data: {e}")
    
    def add_network_data(self, data: Dict[str, Any]):
        """Add new network data and broadcast to clients"""
        self.captured_data.append(data)
        socketio.emit('new_network_data', data)
    
    def add_auth_data(self, data: Dict[str, Any]):
        """Add new auth data and broadcast to clients"""
        self.auth_data.append(data)
        socketio.emit('new_auth_data', data)
    
    def get_summary_stats(self) -> Dict[str, Any]:
        """Get summary statistics"""
        return {
            'total_network_entries': len(self.captured_data),
            'total_auth_entries': len(self.auth_data),
            'unique_processes': len(set(entry.get('process_name', '') 
                                      for entry in self.captured_data)),
            'unique_remote_ips': len(set(entry.get('remote_address', '').split(':')[0] 
                                       for entry in self.captured_data 
                                       if entry.get('remote_address'))),
            'monitoring_active': self.monitoring,
            'fingerprint_available': bool(self.fingerprint_data)
        }

dashboard_manager = DashboardManager()

@app.route('/')
def index():
    """Main dashboard page"""
    return render_template('dashboard.html')

@app.route('/api/network_data')
def get_network_data():
    """Get network data with pagination"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 50, type=int)
    
    start = (page - 1) * per_page
    end = start + per_page
    
    return jsonify({
        'data': dashboard_manager.captured_data[start:end],
        'total': len(dashboard_manager.captured_data),
        'page': page,
        'per_page': per_page
    })

@app.route('/api/auth_data')
def get_auth_data():
    """Get authentication data"""
    return jsonify({
        'data': dashboard_manager.auth_data,
        'total': len(dashboard_manager.auth_data)
    })

@app.route('/api/fingerprint_data')
def get_fingerprint_data():
    """Get hardware fingerprint data"""
    return jsonify(dashboard_manager.fingerprint_data)

@app.route('/api/stats')
def get_stats():
    """Get summary statistics"""
    return jsonify(dashboard_manager.get_summary_stats())

@app.route('/api/search')
def search_data():
    """Search through captured data"""
    query = request.args.get('q', '').lower()
    data_type = request.args.get('type', 'network')
    
    if data_type == 'network':
        results = [entry for entry in dashboard_manager.captured_data
                  if query in json.dumps(entry).lower()]
    elif data_type == 'auth':
        results = [entry for entry in dashboard_manager.auth_data
                  if query in json.dumps(entry).lower()]
    else:
        results = []
    
    return jsonify({
        'results': results,
        'count': len(results),
        'query': query
    })

@socketio.on('connect')
def handle_connect():
    """Handle client connection"""
    logger.info("Client connected to dashboard")
    emit('connected', {'status': 'Connected to Z3X Monitor Dashboard'})

@socketio.on('disconnect')
def handle_disconnect():
    """Handle client disconnection"""
    logger.info("Client disconnected from dashboard")

@socketio.on('request_stats')
def handle_stats_request():
    """Handle stats request"""
    emit('stats_update', dashboard_manager.get_summary_stats())

def create_dashboard_template():
    """Create the HTML template for the dashboard"""
    template_dir = 'templates'
    if not os.path.exists(template_dir):
        os.makedirs(template_dir)
    
    html_content = '''<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Z3X Monitor Dashboard</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .header { background: #2c3e50; color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 20px; }
        .stat-card { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .stat-value { font-size: 2em; font-weight: bold; color: #3498db; }
        .stat-label { color: #7f8c8d; margin-top: 5px; }
        .tabs { display: flex; background: white; border-radius: 8px; overflow: hidden; margin-bottom: 20px; }
        .tab { padding: 15px 25px; cursor: pointer; border-bottom: 3px solid transparent; }
        .tab.active { background: #3498db; color: white; border-bottom-color: #2980b9; }
        .content { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .data-table { width: 100%; border-collapse: collapse; }
        .data-table th, .data-table td { padding: 10px; text-align: left; border-bottom: 1px solid #ecf0f1; }
        .data-table th { background: #34495e; color: white; }
        .search-box { width: 100%; padding: 10px; margin-bottom: 20px; border: 1px solid #bdc3c7; border-radius: 4px; }
        .alert { padding: 15px; margin-bottom: 20px; border-radius: 4px; }
        .alert-warning { background: #f39c12; color: white; }
        .alert-info { background: #3498db; color: white; }
        .json-viewer { background: #2c3e50; color: #ecf0f1; padding: 15px; border-radius: 4px; overflow-x: auto; font-family: monospace; }
        .status-indicator { display: inline-block; width: 10px; height: 10px; border-radius: 50%; margin-right: 5px; }
        .status-active { background: #27ae60; }
        .status-inactive { background: #e74c3c; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔍 Z3X Samstool Monitor Dashboard</h1>
        <p>Real-time monitoring of Z3X communications and PC identification</p>
        <div id="connection-status">
            <span class="status-indicator status-inactive"></span>
            <span id="status-text">Connecting...</span>
        </div>
    </div>

    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-value" id="network-count">0</div>
            <div class="stat-label">Network Entries</div>
        </div>
        <div class="stat-card">
            <div class="stat-value" id="auth-count">0</div>
            <div class="stat-label">Auth Attempts</div>
        </div>
        <div class="stat-card">
            <div class="stat-value" id="process-count">0</div>
            <div class="stat-label">Unique Processes</div>
        </div>
        <div class="stat-card">
            <div class="stat-value" id="ip-count">0</div>
            <div class="stat-label">Remote IPs</div>
        </div>
    </div>

    <div class="tabs">
        <div class="tab active" onclick="showTab('network')">Network Traffic</div>
        <div class="tab" onclick="showTab('auth')">Authentication</div>
        <div class="tab" onclick="showTab('fingerprint')">Hardware Fingerprint</div>
        <div class="tab" onclick="showTab('analysis')">Analysis</div>
    </div>

    <div class="content">
        <div id="network-tab" class="tab-content">
            <input type="text" class="search-box" placeholder="Search network data..." onkeyup="searchData('network', this.value)">
            <div id="network-data">Loading network data...</div>
        </div>

        <div id="auth-tab" class="tab-content" style="display: none;">
            <input type="text" class="search-box" placeholder="Search auth data..." onkeyup="searchData('auth', this.value)">
            <div id="auth-data">Loading authentication data...</div>
        </div>

        <div id="fingerprint-tab" class="tab-content" style="display: none;">
            <div id="fingerprint-data">Loading fingerprint data...</div>
        </div>

        <div id="analysis-tab" class="tab-content" style="display: none;">
            <div class="alert alert-info">
                <strong>Analysis Results:</strong> This section will show potential security issues and recommendations.
            </div>
            <div id="analysis-results">Analyzing captured data...</div>
        </div>
    </div>

    <script>
        const socket = io();
        let currentTab = 'network';

        socket.on('connect', function() {
            document.getElementById('status-text').textContent = 'Connected';
            document.querySelector('.status-indicator').className = 'status-indicator status-active';
            loadInitialData();
        });

        socket.on('disconnect', function() {
            document.getElementById('status-text').textContent = 'Disconnected';
            document.querySelector('.status-indicator').className = 'status-indicator status-inactive';
        });

        socket.on('new_network_data', function(data) {
            addNetworkEntry(data);
            updateStats();
        });

        socket.on('new_auth_data', function(data) {
            addAuthEntry(data);
            updateStats();
        });

        function showTab(tabName) {
            document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
            document.querySelectorAll('.tab-content').forEach(content => content.style.display = 'none');
            
            event.target.classList.add('active');
            document.getElementById(tabName + '-tab').style.display = 'block';
            currentTab = tabName;
        }

        function loadInitialData() {
            fetch('/api/stats').then(r => r.json()).then(updateStatsDisplay);
            fetch('/api/network_data').then(r => r.json()).then(displayNetworkData);
            fetch('/api/auth_data').then(r => r.json()).then(displayAuthData);
            fetch('/api/fingerprint_data').then(r => r.json()).then(displayFingerprintData);
        }

        function updateStatsDisplay(stats) {
            document.getElementById('network-count').textContent = stats.total_network_entries;
            document.getElementById('auth-count').textContent = stats.total_auth_entries;
            document.getElementById('process-count').textContent = stats.unique_processes;
            document.getElementById('ip-count').textContent = stats.unique_remote_ips;
        }

        function displayNetworkData(response) {
            const container = document.getElementById('network-data');
            if (response.data.length === 0) {
                container.innerHTML = '<p>No network data captured yet.</p>';
                return;
            }

            let html = '<table class="data-table"><thead><tr><th>Timestamp</th><th>Process</th><th>Local</th><th>Remote</th><th>Status</th></tr></thead><tbody>';
            response.data.forEach(entry => {
                html += `<tr>
                    <td>${entry.timestamp}</td>
                    <td>${entry.process_name || 'N/A'}</td>
                    <td>${entry.local_address || 'N/A'}</td>
                    <td>${entry.remote_address || 'N/A'}</td>
                    <td>${entry.status || 'N/A'}</td>
                </tr>`;
            });
            html += '</tbody></table>';
            container.innerHTML = html;
        }

        function displayAuthData(response) {
            const container = document.getElementById('auth-data');
            if (response.data.length === 0) {
                container.innerHTML = '<p>No authentication data captured yet.</p>';
                return;
            }

            let html = '<table class="data-table"><thead><tr><th>Timestamp</th><th>URL</th><th>Method</th><th>Credentials</th><th>Device Info</th></tr></thead><tbody>';
            response.data.forEach(entry => {
                html += `<tr>
                    <td>${entry.timestamp}</td>
                    <td>${entry.url}</td>
                    <td>${entry.method}</td>
                    <td>${Object.keys(entry.credentials || {}).length > 0 ? '⚠️ Found' : 'None'}</td>
                    <td>${Object.keys(entry.device_info || {}).length > 0 ? '🔍 Found' : 'None'}</td>
                </tr>`;
            });
            html += '</tbody></table>';
            container.innerHTML = html;
        }

        function displayFingerprintData(data) {
            const container = document.getElementById('fingerprint-data');
            if (Object.keys(data).length === 0) {
                container.innerHTML = '<p>No fingerprint data available. Run the hardware fingerprint detector first.</p>';
                return;
            }

            container.innerHTML = `<div class="json-viewer">${JSON.stringify(data, null, 2)}</div>`;
        }

        function searchData(type, query) {
            if (query.length < 2) {
                loadInitialData();
                return;
            }

            fetch(`/api/search?q=${encodeURIComponent(query)}&type=${type}`)
                .then(r => r.json())
                .then(response => {
                    if (type === 'network') {
                        displayNetworkData({data: response.results});
                    } else if (type === 'auth') {
                        displayAuthData({data: response.results});
                    }
                });
        }

        // Auto-refresh stats every 5 seconds
        setInterval(() => {
            fetch('/api/stats').then(r => r.json()).then(updateStatsDisplay);
        }, 5000);
    </script>
</body>
</html>'''
    
    with open(os.path.join(template_dir, 'dashboard.html'), 'w') as f:
        f.write(html_content)

def start_dashboard(host='127.0.0.1', port=5000):
    """Start the dashboard web server"""
    create_dashboard_template()
    dashboard_manager.load_existing_data()
    
    logger.info(f"Starting dashboard on http://{host}:{port}")
    socketio.run(app, host=host, port=port, debug=False)

if __name__ == "__main__":
    print("Z3X Monitor Dashboard")
    print("====================")
    print("Starting web dashboard...")
    print("Access at: http://127.0.0.1:5000")
    
    start_dashboard()
