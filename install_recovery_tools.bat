@echo off
echo ================================================================
echo Z3X RECOVERY TOOLS - INSTALACION AUTOMATICA
echo ================================================================
echo Este script instalara todas las herramientas necesarias para
echo recuperar el acceso a tu cuenta Z3X Samstool.
echo ================================================================

:: Verificar privilegios de administrador
net session >nul 2>&1
if %errorLevel% == 0 (
    echo [OK] Ejecutandose como Administrador
) else (
    echo [ADVERTENCIA] No se esta ejecutando como Administrador
    echo Algunas funciones pueden no funcionar correctamente.
    pause
)

:: Verificar Python
echo.
echo Verificando Python...
python --version >nul 2>&1
if %errorLevel% == 0 (
    echo [OK] Python encontrado
    python --version
) else (
    echo [ERROR] Python no encontrado
    echo Por favor instala Python desde https://python.org
    pause
    exit /b 1
)

:: Verificar pip
echo.
echo Verificando pip...
pip --version >nul 2>&1
if %errorLevel% == 0 (
    echo [OK] pip encontrado
) else (
    echo [ERROR] pip no encontrado
    echo Instalando pip...
    python -m ensurepip --upgrade
)

:: Instalar dependencias
echo.
echo ================================================================
echo INSTALANDO DEPENDENCIAS PYTHON
echo ================================================================

echo Instalando psutil...
pip install psutil

echo Instalando WMI...
pip install WMI

echo Instalando requests...
pip install requests

echo Instalando flask...
pip install flask

echo Instalando flask-socketio...
pip install flask-socketio

echo Instalando mitmproxy...
pip install mitmproxy

echo Instalando pandas...
pip install pandas

echo Instalando matplotlib...
pip install matplotlib

echo Instalando seaborn...
pip install seaborn

echo Instalando cryptography...
pip install cryptography

echo Instalando pywin32...
pip install pywin32

echo.
echo ================================================================
echo VERIFICANDO INSTALACION
echo ================================================================

:: Verificar que todos los módulos se instalaron correctamente
python -c "import psutil; print('psutil: OK')" 2>nul || echo "psutil: ERROR"
python -c "import wmi; print('wmi: OK')" 2>nul || echo "wmi: ERROR"
python -c "import requests; print('requests: OK')" 2>nul || echo "requests: ERROR"
python -c "import flask; print('flask: OK')" 2>nul || echo "flask: ERROR"
python -c "import flask_socketio; print('flask_socketio: OK')" 2>nul || echo "flask_socketio: ERROR"
python -c "import mitmproxy; print('mitmproxy: OK')" 2>nul || echo "mitmproxy: ERROR"
python -c "import pandas; print('pandas: OK')" 2>nul || echo "pandas: ERROR"
python -c "import matplotlib; print('matplotlib: OK')" 2>nul || echo "matplotlib: ERROR"
python -c "import seaborn; print('seaborn: OK')" 2>nul || echo "seaborn: ERROR"

echo.
echo ================================================================
echo CREANDO ACCESOS DIRECTOS
echo ================================================================

:: Crear script de inicio rápido
echo @echo off > start_z3x_recovery.bat
echo echo Iniciando Z3X Recovery Tool... >> start_z3x_recovery.bat
echo python z3x_recovery_tool.py >> start_z3x_recovery.bat
echo pause >> start_z3x_recovery.bat

echo [OK] Acceso directo creado: start_z3x_recovery.bat

:: Crear script de escaneo rápido
echo @echo off > quick_scan.bat
echo echo Iniciando escaneo rapido de archivos Z3X... >> quick_scan.bat
echo python z3x_file_scanner.py >> quick_scan.bat
echo pause >> quick_scan.bat

echo [OK] Acceso directo creado: quick_scan.bat

:: Crear script de análisis de registro
echo @echo off > registry_scan.bat
echo echo Iniciando escaneo del registro... >> registry_scan.bat
echo python z3x_registry_scanner.py >> registry_scan.bat
echo pause >> registry_scan.bat

echo [OK] Acceso directo creado: registry_scan.bat

echo.
echo ================================================================
echo CONFIGURACION DE FIREWALL
echo ================================================================

echo Configurando reglas de firewall para monitoreo...

:: Permitir Python a través del firewall
netsh advfirewall firewall add rule name="Z3X Recovery - Python" dir=in action=allow program="%SYSTEMROOT%\System32\python.exe" enable=yes >nul 2>&1
netsh advfirewall firewall add rule name="Z3X Recovery - Python Scripts" dir=in action=allow program="python.exe" enable=yes >nul 2>&1

echo [OK] Reglas de firewall configuradas

echo.
echo ================================================================
echo CREANDO ESTRUCTURA DE DIRECTORIOS
echo ================================================================

:: Crear directorios para logs y reportes
if not exist "logs" mkdir logs
if not exist "reports" mkdir reports
if not exist "backups" mkdir backups
if not exist "exports" mkdir exports

echo [OK] Directorios creados:
echo     - logs/     (archivos de log)
echo     - reports/  (reportes de analisis)
echo     - backups/  (copias de seguridad)
echo     - exports/  (datos exportados)

echo.
echo ================================================================
echo CONFIGURACION COMPLETADA
echo ================================================================
echo.
echo Las herramientas de recuperacion Z3X han sido instaladas exitosamente.
echo.
echo ARCHIVOS PRINCIPALES:
echo   z3x_recovery_tool.py     - Herramienta principal de recuperacion
echo   z3x_file_scanner.py      - Escaner de archivos Z3X
echo   z3x_config_analyzer.py   - Analizador de configuracion
echo   z3x_registry_scanner.py  - Escaner del registro
echo.
echo ACCESOS DIRECTOS:
echo   start_z3x_recovery.bat   - Iniciar herramienta principal
echo   quick_scan.bat           - Escaneo rapido de archivos
echo   registry_scan.bat        - Escaneo del registro
echo.
echo PROXIMOS PASOS:
echo 1. Ejecutar: start_z3x_recovery.bat
echo 2. Seleccionar "Analisis completo del sistema"
echo 3. Seguir las recomendaciones generadas
echo.
echo ================================================================

pause
