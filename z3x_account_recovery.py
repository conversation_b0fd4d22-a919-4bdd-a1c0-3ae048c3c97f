#!/usr/bin/env python3
"""
Z3X Account Recovery Tool - Herramienta específica para recuperar acceso a cuenta Z3X
"""

import os
import sys
import json
import winreg
import subprocess
import shutil
import random
import string
from datetime import datetime

class Z3XAccountRecovery:
    def __init__(self):
        self.backup_dir = f"z3x_recovery_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.original_hwid = {}
        self.new_hwid = {}
        
    def check_admin_privileges(self):
        """Verifica privilegios de administrador"""
        try:
            import ctypes
            return ctypes.windll.shell32.IsUserAnAdmin()
        except:
            return False
    
    def create_system_backup(self):
        """Crea backup completo antes de hacer cambios"""
        print(f"💾 Creando backup del sistema en: {self.backup_dir}")
        
        if not os.path.exists(self.backup_dir):
            os.makedirs(self.backup_dir)
        
        # Backup del registro
        try:
            reg_backup = os.path.join(self.backup_dir, "registry_backup.reg")
            cmd = f'reg export HKLM "{reg_backup}"'
            subprocess.run(cmd, shell=True, check=True)
            print(f"✅ Backup del registro: {reg_backup}")
        except Exception as e:
            print(f"❌ Error en backup del registro: {e}")
        
        # Backup de archivos Z3X
        try:
            z3x_backup = os.path.join(self.backup_dir, "z3x_files")
            os.makedirs(z3x_backup, exist_ok=True)
            
            # Copiar archivos de configuración Z3X
            z3x_appdata = os.path.expanduser(r"~\AppData\Roaming\z3x")
            if os.path.exists(z3x_appdata):
                shutil.copytree(z3x_appdata, os.path.join(z3x_backup, "appdata_z3x"), dirs_exist_ok=True)
                print(f"✅ Backup de archivos Z3X: {z3x_backup}")
        except Exception as e:
            print(f"❌ Error en backup de archivos Z3X: {e}")
        
        return self.backup_dir
    
    def load_current_hwid(self):
        """Carga la información actual de hardware"""
        try:
            # Buscar el archivo más reciente de hardware fingerprint
            import glob
            hw_files = glob.glob("z3x_hardware_fingerprint_*.json")
            
            if hw_files:
                latest_file = sorted(hw_files)[-1]
                with open(latest_file, 'r', encoding='utf-8') as f:
                    self.original_hwid = json.load(f)
                print(f"✅ Información de hardware cargada desde: {latest_file}")
                return True
            else:
                print("❌ No se encontró información de hardware. Ejecuta extract_z3x_hwid.py primero.")
                return False
        except Exception as e:
            print(f"❌ Error cargando información de hardware: {e}")
            return False
    
    def generate_new_mac_address(self):
        """Genera una nueva dirección MAC válida"""
        # Generar MAC con prefijo de fabricante común
        prefixes = [
            "00:1B:44",  # ASIX Electronics
            "00:50:B6",  # GOOD WAY IND
            "00:E0:4C",  # Realtek
            "08:00:27",  # VirtualBox
            "52:54:00"   # QEMU/KVM
        ]
        
        prefix = random.choice(prefixes)
        suffix = ":".join([f"{random.randint(0, 255):02X}" for _ in range(3)])
        return f"{prefix}:{suffix}"
    
    def change_mac_address(self, adapter_name, new_mac):
        """Cambia la dirección MAC de un adaptador"""
        try:
            print(f"🔄 Cambiando MAC de {adapter_name} a {new_mac}")
            
            # Método 1: Usar netsh (Windows 10/11)
            cmd = f'netsh interface set interface "{adapter_name}" admin=disable'
            subprocess.run(cmd, shell=True, check=True)
            
            # Buscar el adaptador en el registro
            reg_path = r"SYSTEM\CurrentControlSet\Control\Class\{4d36e972-e325-11ce-bfc1-08002be10318}"
            
            with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, reg_path) as key:
                i = 0
                while True:
                    try:
                        subkey_name = winreg.EnumKey(key, i)
                        subkey_path = f"{reg_path}\\{subkey_name}"
                        
                        with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, subkey_path) as subkey:
                            try:
                                desc, _ = winreg.QueryValueEx(subkey, "DriverDesc")
                                if adapter_name.lower() in desc.lower():
                                    # Cambiar MAC en el registro
                                    mac_clean = new_mac.replace(":", "").replace("-", "")
                                    winreg.SetValueEx(subkey, "NetworkAddress", 0, winreg.REG_SZ, mac_clean)
                                    print(f"✅ MAC cambiada en el registro")
                                    break
                            except FileNotFoundError:
                                pass
                        i += 1
                    except WindowsError:
                        break
            
            # Reactivar adaptador
            cmd = f'netsh interface set interface "{adapter_name}" admin=enable'
            subprocess.run(cmd, shell=True, check=True)
            
            return True
            
        except Exception as e:
            print(f"❌ Error cambiando MAC: {e}")
            return False
    
    def modify_machine_guid(self):
        """Modifica el Machine GUID de Windows"""
        try:
            print("🔄 Modificando Machine GUID...")
            
            # Generar nuevo GUID
            import uuid
            new_guid = str(uuid.uuid4())
            
            # Modificar en el registro
            with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Microsoft\Cryptography", 
                              0, winreg.KEY_SET_VALUE) as key:
                winreg.SetValueEx(key, "MachineGuid", 0, winreg.REG_SZ, new_guid)
            
            print(f"✅ Machine GUID cambiado a: {new_guid}")
            self.new_hwid['machine_guid'] = new_guid
            return True
            
        except Exception as e:
            print(f"❌ Error modificando Machine GUID: {e}")
            return False
    
    def clean_z3x_registry_entries(self):
        """Limpia entradas Z3X del registro"""
        try:
            print("🧹 Limpiando entradas Z3X del registro...")
            
            # Eliminar claves Z3X
            z3x_keys = [
                r"SOFTWARE\Z3X",
                r"SOFTWARE\WOW6432Node\Z3X"
            ]
            
            for key_path in z3x_keys:
                try:
                    winreg.DeleteKey(winreg.HKEY_LOCAL_MACHINE, key_path)
                    print(f"✅ Eliminada clave: HKLM\\{key_path}")
                except FileNotFoundError:
                    pass
                except Exception as e:
                    print(f"❌ Error eliminando {key_path}: {e}")
                
                try:
                    winreg.DeleteKey(winreg.HKEY_CURRENT_USER, key_path)
                    print(f"✅ Eliminada clave: HKCU\\{key_path}")
                except FileNotFoundError:
                    pass
                except Exception as e:
                    print(f"❌ Error eliminando {key_path}: {e}")
            
            return True
            
        except Exception as e:
            print(f"❌ Error limpiando registro: {e}")
            return False
    
    def clean_z3x_files(self):
        """Limpia archivos de configuración Z3X"""
        try:
            print("🧹 Limpiando archivos de configuración Z3X...")
            
            # Eliminar configuración de usuario
            z3x_appdata = os.path.expanduser(r"~\AppData\Roaming\z3x")
            if os.path.exists(z3x_appdata):
                shutil.rmtree(z3x_appdata)
                print(f"✅ Eliminado directorio: {z3x_appdata}")
            
            # Limpiar archivos temporales
            temp_dirs = [
                os.environ.get('TEMP', ''),
                os.path.expanduser(r"~\AppData\Local\Temp")
            ]
            
            for temp_dir in temp_dirs:
                if os.path.exists(temp_dir):
                    for root, dirs, files in os.walk(temp_dir):
                        for file in files:
                            if 'z3x' in file.lower() or 'samstool' in file.lower():
                                try:
                                    file_path = os.path.join(root, file)
                                    os.remove(file_path)
                                    print(f"✅ Eliminado archivo temporal: {file}")
                                except:
                                    pass
            
            return True
            
        except Exception as e:
            print(f"❌ Error limpiando archivos: {e}")
            return False
    
    def spoof_hardware_ids(self):
        """Modifica identificadores de hardware"""
        try:
            print("🎭 Modificando identificadores de hardware...")
            
            # Cambiar MAC addresses
            mac_addresses = self.original_hwid.get('network', {}).get('mac_addresses', [])
            
            if mac_addresses:
                primary_adapter = mac_addresses[0]['adapter']
                new_mac = self.generate_new_mac_address()
                
                if self.change_mac_address(primary_adapter, new_mac):
                    self.new_hwid['primary_mac'] = new_mac
                    print(f"✅ MAC principal cambiada a: {new_mac}")
            
            # Modificar Machine GUID
            if self.modify_machine_guid():
                print("✅ Machine GUID modificado")
            
            return True
            
        except Exception as e:
            print(f"❌ Error modificando hardware: {e}")
            return False
    
    def create_vm_instructions(self):
        """Crea instrucciones para usar máquina virtual"""
        instructions = """
# INSTRUCCIONES PARA USAR MÁQUINA VIRTUAL

## Opción 1: VirtualBox (Gratis)
1. Descargar VirtualBox: https://www.virtualbox.org/
2. Crear nueva VM con Windows 10/11
3. Configurar hardware diferente:
   - RAM: 4GB mínimo
   - Disco: 50GB mínimo
   - Red: NAT o Bridge
4. Instalar Windows en la VM
5. Instalar Z3X Samstool en la VM
6. Usar cuenta desde la VM

## Opción 2: VMware (Mejor rendimiento)
1. Descargar VMware Workstation
2. Crear nueva VM
3. Configurar hardware único
4. Instalar sistema operativo
5. Instalar Z3X

## Ventajas de VM:
- Hardware completamente diferente
- Fácil de resetear si hay problemas
- Aislamiento del sistema principal
- Múltiples configuraciones posibles

## Configuración recomendada:
- Generar MAC address único
- Usar UUID diferente
- Configurar CPU virtual diferente
- Usar disco virtual nuevo
"""
        
        vm_file = os.path.join(self.backup_dir, "vm_instructions.txt")
        with open(vm_file, 'w', encoding='utf-8') as f:
            f.write(instructions)
        
        print(f"📋 Instrucciones de VM guardadas en: {vm_file}")
    
    def generate_recovery_report(self):
        """Genera reporte de recuperación"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"z3x_recovery_report_{timestamp}.json"
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'backup_directory': self.backup_dir,
            'original_hwid': self.original_hwid.get('critical_identifiers', {}),
            'new_hwid': self.new_hwid,
            'actions_performed': [],
            'next_steps': [
                "Reiniciar el sistema",
                "Verificar que los cambios se aplicaron",
                "Intentar acceder a Z3X Samstool",
                "Si no funciona, usar máquina virtual",
                "Contactar soporte Z3X si es necesario"
            ],
            'recovery_methods': {
                'hardware_spoofing': 'Modificación de identificadores de hardware',
                'registry_cleanup': 'Limpieza de entradas Z3X en registro',
                'file_cleanup': 'Eliminación de archivos de configuración',
                'virtual_machine': 'Uso de máquina virtual con hardware diferente'
            }
        }
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"📊 Reporte de recuperación guardado: {report_file}")
        return report_file
    
    def execute_recovery(self):
        """Ejecuta el proceso completo de recuperación"""
        print("="*70)
        print("🚀 Z3X ACCOUNT RECOVERY - PROCESO DE RECUPERACIÓN")
        print("="*70)
        
        if not self.check_admin_privileges():
            print("❌ ERROR: Se requieren privilegios de Administrador")
            print("Ejecuta este script como Administrador")
            return False
        
        # Cargar información actual
        if not self.load_current_hwid():
            return False
        
        # Crear backup
        backup_dir = self.create_system_backup()
        
        print(f"\n{'='*50}")
        print("SELECCIONA EL MÉTODO DE RECUPERACIÓN:")
        print("="*50)
        print("1. 🎭 Modificar identificadores de hardware (Recomendado)")
        print("2. 🧹 Solo limpiar archivos y registro Z3X")
        print("3. 💻 Crear instrucciones para máquina virtual")
        print("4. 🔄 Proceso completo (Todo lo anterior)")
        print("5. ❌ Cancelar")
        
        choice = input("\nSelecciona una opción (1-5): ").strip()
        
        if choice == '1':
            print(f"\n🎭 MODIFICANDO IDENTIFICADORES DE HARDWARE")
            print("="*50)
            self.spoof_hardware_ids()
            
        elif choice == '2':
            print(f"\n🧹 LIMPIANDO ARCHIVOS Y REGISTRO Z3X")
            print("="*50)
            self.clean_z3x_registry_entries()
            self.clean_z3x_files()
            
        elif choice == '3':
            print(f"\n💻 CREANDO INSTRUCCIONES PARA VM")
            print("="*50)
            self.create_vm_instructions()
            
        elif choice == '4':
            print(f"\n🔄 EJECUTANDO PROCESO COMPLETO")
            print("="*50)
            self.spoof_hardware_ids()
            self.clean_z3x_registry_entries()
            self.clean_z3x_files()
            self.create_vm_instructions()
            
        elif choice == '5':
            print("❌ Operación cancelada")
            return False
        
        else:
            print("❌ Opción inválida")
            return False
        
        # Generar reporte
        report_file = self.generate_recovery_report()
        
        print(f"\n" + "="*70)
        print("✅ PROCESO DE RECUPERACIÓN COMPLETADO")
        print("="*70)
        print(f"📁 Backup creado en: {backup_dir}")
        print(f"📊 Reporte generado: {report_file}")
        print(f"\n🔄 PRÓXIMOS PASOS:")
        print("1. Reiniciar el sistema")
        print("2. Intentar acceder a Z3X Samstool")
        print("3. Si no funciona, usar máquina virtual")
        print("4. Contactar soporte Z3X si es necesario")
        print("="*70)
        
        return True

def main():
    recovery = Z3XAccountRecovery()
    
    print("Z3X Account Recovery Tool")
    print("=========================")
    print("Esta herramienta te ayudará a recuperar el acceso a tu cuenta Z3X")
    print("modificando los identificadores de hardware que Z3X usa.")
    print()
    print("⚠️  IMPORTANTE:")
    print("- Se requieren privilegios de Administrador")
    print("- Se creará un backup completo antes de hacer cambios")
    print("- Algunos cambios requieren reinicio del sistema")
    print()
    
    confirm = input("¿Continuar con la recuperación? (s/n): ")
    if confirm.lower() != 's':
        print("Operación cancelada.")
        return
    
    try:
        success = recovery.execute_recovery()
        
        if success:
            print(f"\n🎉 ¡Recuperación completada! Reinicia el sistema e intenta acceder a Z3X.")
        else:
            print(f"\n❌ Error durante la recuperación. Revisa los logs.")
            
    except Exception as e:
        print(f"\nError: {e}")

if __name__ == "__main__":
    main()
