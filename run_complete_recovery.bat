@echo off
echo ================================================================
echo Z3X SAMSTOOL - RECUPERACION COMPLETA DE CUENTA
echo ================================================================
echo Esta herramienta ejecutara un proceso completo para recuperar
echo el acceso a tu cuenta Z3X Samstool que fue comprometida.
echo ================================================================

:: Verificar privilegios de administrador
net session >nul 2>&1
if %errorLevel% == 0 (
    echo [OK] Ejecutandose como Administrador
) else (
    echo [ERROR] Se requieren privilegios de Administrador
    echo Haz clic derecho en este archivo y selecciona "Ejecutar como administrador"
    pause
    exit /b 1
)

echo.
echo ================================================================
echo PASO 1: EXTRAYENDO INFORMACION DE HARDWARE ACTUAL
echo ================================================================
echo Analizando como Z3X identifica tu PC...

python extract_z3x_hwid.py

if %errorLevel% neq 0 (
    echo [ERROR] Error extrayendo informacion de hardware
    pause
    exit /b 1
)

echo.
echo ================================================================
echo PASO 2: ESCANEANDO REGISTRO DE WINDOWS
echo ================================================================
echo Buscando entradas Z3X en el registro...

echo. | python z3x_registry_scanner.py

if %errorLevel% neq 0 (
    echo [ERROR] Error escaneando registro
    pause
    exit /b 1
)

echo.
echo ================================================================
echo PASO 3: CREANDO BACKUP COMPLETO DEL SISTEMA
echo ================================================================
echo Creando backup de seguridad antes de hacer cambios...

:: Crear directorio de backup
set BACKUP_DIR=z3x_complete_backup_%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%%time:~6,2%
set BACKUP_DIR=%BACKUP_DIR: =0%
mkdir "%BACKUP_DIR%" 2>nul

:: Backup del registro completo
echo Creando backup del registro...
reg export HKLM "%BACKUP_DIR%\registry_backup_HKLM.reg" /y >nul 2>&1
reg export HKCU "%BACKUP_DIR%\registry_backup_HKCU.reg" /y >nul 2>&1

:: Backup de archivos Z3X
echo Creando backup de archivos Z3X...
if exist "C:\Program Files (x86)\Z3X" (
    xcopy "C:\Program Files (x86)\Z3X" "%BACKUP_DIR%\z3x_program_files\" /E /I /H /Y >nul 2>&1
)

if exist "%APPDATA%\z3x" (
    xcopy "%APPDATA%\z3x" "%BACKUP_DIR%\z3x_appdata\" /E /I /H /Y >nul 2>&1
)

echo [OK] Backup creado en: %BACKUP_DIR%

echo.
echo ================================================================
echo PASO 4: MOSTRANDO INFORMACION CRITICA ENCONTRADA
echo ================================================================

:: Mostrar resumen de la informacion encontrada
echo INFORMACION CRITICA DE HARDWARE DETECTADA:
echo.

:: Buscar el archivo de hardware mas reciente
for /f "delims=" %%i in ('dir /b /o-d z3x_hardware_fingerprint_*.json 2^>nul') do (
    set LATEST_HW_FILE=%%i
    goto :found_hw
)
:found_hw

if defined LATEST_HW_FILE (
    echo Archivo de hardware: %LATEST_HW_FILE%
    
    :: Extraer informacion critica usando PowerShell
    powershell -Command "& {$hw = Get-Content '%LATEST_HW_FILE%' | ConvertFrom-Json; Write-Host 'Machine GUID:' $hw.critical_identifiers.machine_guid; Write-Host 'CPU ID:' $hw.critical_identifiers.cpu.identifier; if($hw.network.mac_addresses) {Write-Host 'Primary MAC:' $hw.network.mac_addresses[0].mac}; if($hw.storage.disks) {Write-Host 'Primary Disk:' $hw.storage.disks[0].serial}}"
) else (
    echo [WARNING] No se encontro informacion de hardware
)

echo.
echo ================================================================
echo PASO 5: OPCIONES DE RECUPERACION
echo ================================================================
echo.
echo Selecciona el metodo de recuperacion:
echo.
echo 1. AUTOMATICO - Modificar identificadores y limpiar archivos (RECOMENDADO)
echo 2. MANUAL - Solo crear instrucciones detalladas
echo 3. VM - Crear maquina virtual con hardware diferente
echo 4. CANCELAR - No hacer cambios
echo.

set /p RECOVERY_CHOICE="Selecciona una opcion (1-4): "

if "%RECOVERY_CHOICE%"=="1" goto :automatic_recovery
if "%RECOVERY_CHOICE%"=="2" goto :manual_instructions
if "%RECOVERY_CHOICE%"=="3" goto :vm_instructions
if "%RECOVERY_CHOICE%"=="4" goto :cancel_recovery

echo [ERROR] Opcion invalida
goto :cancel_recovery

:automatic_recovery
echo.
echo ================================================================
echo EJECUTANDO RECUPERACION AUTOMATICA
echo ================================================================
echo.
echo ADVERTENCIA: Este proceso modificara tu sistema.
echo Se ha creado un backup completo en: %BACKUP_DIR%
echo.
set /p CONFIRM="¿Continuar con la modificacion automatica? (s/n): "

if /i not "%CONFIRM%"=="s" goto :cancel_recovery

echo.
echo Ejecutando recuperacion automatica...
python z3x_account_recovery.py

echo.
echo ================================================================
echo RECUPERACION AUTOMATICA COMPLETADA
echo ================================================================
echo.
echo PROXIMOS PASOS:
echo 1. REINICIA tu computadora
echo 2. Intenta acceder a Z3X Samstool
echo 3. Si no funciona, usa la opcion de maquina virtual
echo 4. Contacta soporte Z3X si es necesario
echo.
echo BACKUP DISPONIBLE EN: %BACKUP_DIR%
echo.
goto :end

:manual_instructions
echo.
echo ================================================================
echo CREANDO INSTRUCCIONES MANUALES
echo ================================================================

:: Crear archivo de instrucciones manuales
set INSTRUCTIONS_FILE=%BACKUP_DIR%\instrucciones_manuales.txt

echo INSTRUCCIONES MANUALES PARA RECUPERAR ACCESO A Z3X SAMSTOOL > "%INSTRUCTIONS_FILE%"
echo ============================================================== >> "%INSTRUCTIONS_FILE%"
echo. >> "%INSTRUCTIONS_FILE%"
echo INFORMACION DE TU HARDWARE ACTUAL: >> "%INSTRUCTIONS_FILE%"

:: Agregar informacion de hardware al archivo
if defined LATEST_HW_FILE (
    powershell -Command "& {$hw = Get-Content '%LATEST_HW_FILE%' | ConvertFrom-Json; 'Machine GUID: ' + $hw.critical_identifiers.machine_guid; 'CPU ID: ' + $hw.critical_identifiers.cpu.identifier; if($hw.network.mac_addresses) {'Primary MAC: ' + $hw.network.mac_addresses[0].mac}; if($hw.storage.disks) {'Primary Disk: ' + $hw.storage.disks[0].serial}}" >> "%INSTRUCTIONS_FILE%"
)

echo. >> "%INSTRUCTIONS_FILE%"
echo METODOS DE RECUPERACION: >> "%INSTRUCTIONS_FILE%"
echo. >> "%INSTRUCTIONS_FILE%"
echo 1. CAMBIAR MAC ADDRESS: >> "%INSTRUCTIONS_FILE%"
echo    - Ir a Administrador de dispositivos >> "%INSTRUCTIONS_FILE%"
echo    - Buscar adaptador de red >> "%INSTRUCTIONS_FILE%"
echo    - Propiedades ^> Avanzado ^> Network Address >> "%INSTRUCTIONS_FILE%"
echo    - Cambiar a una MAC diferente >> "%INSTRUCTIONS_FILE%"
echo. >> "%INSTRUCTIONS_FILE%"
echo 2. LIMPIAR REGISTRO: >> "%INSTRUCTIONS_FILE%"
echo    - Abrir regedit como administrador >> "%INSTRUCTIONS_FILE%"
echo    - Eliminar: HKLM\SOFTWARE\Z3X >> "%INSTRUCTIONS_FILE%"
echo    - Eliminar: HKCU\SOFTWARE\Z3X >> "%INSTRUCTIONS_FILE%"
echo. >> "%INSTRUCTIONS_FILE%"
echo 3. LIMPIAR ARCHIVOS: >> "%INSTRUCTIONS_FILE%"
echo    - Eliminar: %%APPDATA%%\z3x >> "%INSTRUCTIONS_FILE%"
echo    - Limpiar archivos temporales con 'z3x' en el nombre >> "%INSTRUCTIONS_FILE%"
echo. >> "%INSTRUCTIONS_FILE%"
echo 4. USAR MAQUINA VIRTUAL: >> "%INSTRUCTIONS_FILE%"
echo    - Instalar VirtualBox o VMware >> "%INSTRUCTIONS_FILE%"
echo    - Crear VM con Windows >> "%INSTRUCTIONS_FILE%"
echo    - Instalar Z3X en la VM >> "%INSTRUCTIONS_FILE%"
echo    - Usar cuenta desde la VM >> "%INSTRUCTIONS_FILE%"

echo [OK] Instrucciones manuales creadas: %INSTRUCTIONS_FILE%
notepad "%INSTRUCTIONS_FILE%"
goto :end

:vm_instructions
echo.
echo ================================================================
echo CREANDO INSTRUCCIONES PARA MAQUINA VIRTUAL
echo ================================================================

:: Crear archivo de instrucciones para VM
set VM_FILE=%BACKUP_DIR%\instrucciones_vm.txt

echo INSTRUCCIONES PARA USAR MAQUINA VIRTUAL > "%VM_FILE%"
echo ========================================== >> "%VM_FILE%"
echo. >> "%VM_FILE%"
echo OPCION 1: VIRTUALBOX (GRATIS) >> "%VM_FILE%"
echo 1. Descargar VirtualBox: https://www.virtualbox.org/ >> "%VM_FILE%"
echo 2. Crear nueva VM con Windows 10/11 >> "%VM_FILE%"
echo 3. Configurar: RAM 4GB, Disco 50GB >> "%VM_FILE%"
echo 4. Instalar Windows en la VM >> "%VM_FILE%"
echo 5. Instalar Z3X Samstool en la VM >> "%VM_FILE%"
echo 6. Usar tu cuenta desde la VM >> "%VM_FILE%"
echo. >> "%VM_FILE%"
echo OPCION 2: VMWARE WORKSTATION >> "%VM_FILE%"
echo 1. Descargar VMware Workstation >> "%VM_FILE%"
echo 2. Crear nueva VM >> "%VM_FILE%"
echo 3. Configurar hardware unico >> "%VM_FILE%"
echo 4. Instalar sistema operativo >> "%VM_FILE%"
echo 5. Instalar Z3X >> "%VM_FILE%"
echo. >> "%VM_FILE%"
echo VENTAJAS: >> "%VM_FILE%"
echo - Hardware completamente diferente >> "%VM_FILE%"
echo - Facil de resetear si hay problemas >> "%VM_FILE%"
echo - Aislamiento del sistema principal >> "%VM_FILE%"

echo [OK] Instrucciones de VM creadas: %VM_FILE%
notepad "%VM_FILE%"
goto :end

:cancel_recovery
echo.
echo ================================================================
echo OPERACION CANCELADA
echo ================================================================
echo No se realizaron cambios en el sistema.
echo El backup se mantiene disponible en: %BACKUP_DIR%
goto :end

:end
echo.
echo ================================================================
echo PROCESO COMPLETADO
echo ================================================================
echo.
echo ARCHIVOS GENERADOS:
echo - Backup completo: %BACKUP_DIR%
echo - Informacion de hardware: %LATEST_HW_FILE%
echo - Escaneo de registro: z3x_registry_scan_*.json
echo.
echo SOPORTE ADICIONAL:
echo Si necesitas ayuda adicional, contacta al soporte de Z3X
echo con la nueva informacion de hardware generada.
echo.
echo ================================================================
pause
