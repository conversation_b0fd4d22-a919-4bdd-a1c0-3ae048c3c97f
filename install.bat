@echo off
echo ================================================================
echo Z3X Samstool Monitor - Installation Script
echo ================================================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8 or higher from https://python.org
    pause
    exit /b 1
)

echo Python found. Installing dependencies...
echo.

REM Install required packages
pip install -r requirements.txt

if errorlevel 1 (
    echo.
    echo ERROR: Failed to install dependencies
    echo Please check your internet connection and try again
    pause
    exit /b 1
)

echo.
echo ================================================================
echo Installation Complete!
echo ================================================================
echo.
echo To start monitoring, run:
echo   python z3x_monitor.py --mode full
echo.
echo For help, run:
echo   python z3x_monitor.py --help
echo.
echo IMPORTANT: Run as Administrator for best results
echo ================================================================
pause
