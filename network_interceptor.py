#!/usr/bin/env python3
"""
Z3X Samstool Network Traffic Interceptor
Captures and analyzes all network communications to identify PC fingerprinting methods
"""

import socket
import threading
import time
import json
import logging
import subprocess
import psutil
import requests
from datetime import datetime
from typing import Dict, List, Any
import winreg
import uuid
import hashlib
import os
import sys

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('z3x_intercept.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class NetworkInterceptor:
    def __init__(self):
        self.captured_data = []
        self.running = False
        self.proxy_port = 8888
        self.target_processes = ['z3x', 'samstool', 'Z3X', 'SAMSTOOL']
        
    def start_packet_capture(self):
        """Start capturing network packets using netsh trace"""
        try:
            # Start Windows netsh trace
            cmd = [
                'netsh', 'trace', 'start',
                'capture=yes',
                'tracefile=z3x_traffic.etl',
                'provider=Microsoft-Windows-TCPIP',
                'keywords=ut:TcpipKeyword'
            ]
            
            logger.info("Starting network packet capture...")
            result = subprocess.run(cmd, capture_output=True, text=True, shell=True)
            
            if result.returncode == 0:
                logger.info("Network capture started successfully")
                return True
            else:
                logger.error(f"Failed to start capture: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"Error starting packet capture: {e}")
            return False
    
    def stop_packet_capture(self):
        """Stop network packet capture"""
        try:
            cmd = ['netsh', 'trace', 'stop']
            result = subprocess.run(cmd, capture_output=True, text=True, shell=True)
            
            if result.returncode == 0:
                logger.info("Network capture stopped successfully")
                return True
            else:
                logger.error(f"Failed to stop capture: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"Error stopping packet capture: {e}")
            return False
    
    def monitor_processes(self):
        """Monitor for Z3X/Samstool processes and their network activity"""
        logger.info("Starting process monitoring...")
        
        while self.running:
            try:
                for proc in psutil.process_iter(['pid', 'name', 'connections']):
                    try:
                        proc_info = proc.info
                        proc_name = proc_info['name'].lower()
                        
                        # Check if this is a Z3X related process
                        if any(target.lower() in proc_name for target in self.target_processes):
                            logger.info(f"Found Z3X process: {proc_info['name']} (PID: {proc_info['pid']})")
                            
                            # Get network connections for this process
                            connections = proc_info.get('connections', [])
                            if connections:
                                self.log_process_connections(proc_info, connections)
                                
                    except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                        continue
                        
                time.sleep(2)  # Check every 2 seconds
                
            except Exception as e:
                logger.error(f"Error in process monitoring: {e}")
                time.sleep(5)
    
    def log_process_connections(self, proc_info: Dict, connections: List):
        """Log network connections for Z3X processes"""
        timestamp = datetime.now().isoformat()
        
        for conn in connections:
            if conn.status == 'ESTABLISHED':
                connection_data = {
                    'timestamp': timestamp,
                    'process_name': proc_info['name'],
                    'process_id': proc_info['pid'],
                    'local_address': f"{conn.laddr.ip}:{conn.laddr.port}",
                    'remote_address': f"{conn.raddr.ip}:{conn.raddr.port}" if conn.raddr else "N/A",
                    'status': conn.status,
                    'family': conn.family.name,
                    'type': conn.type.name
                }
                
                self.captured_data.append(connection_data)
                logger.info(f"Z3X Connection: {connection_data}")
    
    def setup_proxy_server(self):
        """Setup a local proxy server to intercept HTTP/HTTPS traffic"""
        try:
            import mitmproxy
            from mitmproxy import http, options
            from mitmproxy.tools.dump import DumpMaster
            
            logger.info(f"Setting up proxy server on port {self.proxy_port}")
            
            # This would require mitmproxy to be installed
            # For now, we'll use a simpler approach with socket monitoring
            return self.setup_socket_monitor()
            
        except ImportError:
            logger.warning("mitmproxy not available, using socket monitoring instead")
            return self.setup_socket_monitor()
    
    def setup_socket_monitor(self):
        """Setup socket monitoring for HTTP traffic"""
        def socket_monitor():
            try:
                # Monitor common HTTP/HTTPS ports
                ports_to_monitor = [80, 443, 8080, 8443, 3000, 5000]
                
                for port in ports_to_monitor:
                    threading.Thread(
                        target=self.monitor_port,
                        args=(port,),
                        daemon=True
                    ).start()
                    
            except Exception as e:
                logger.error(f"Error setting up socket monitor: {e}")
        
        threading.Thread(target=socket_monitor, daemon=True).start()
        return True
    
    def monitor_port(self, port: int):
        """Monitor specific port for connections"""
        try:
            # This is a simplified monitoring approach
            # In a real implementation, you'd want to use raw sockets or pcap
            logger.info(f"Monitoring port {port} for Z3X traffic")
            
            while self.running:
                # Check for active connections on this port
                for conn in psutil.net_connections():
                    if (conn.laddr and conn.laddr.port == port and 
                        conn.status == 'ESTABLISHED'):
                        
                        # Try to identify if this belongs to Z3X
                        try:
                            proc = psutil.Process(conn.pid) if conn.pid else None
                            if proc and any(target.lower() in proc.name().lower() 
                                          for target in self.target_processes):
                                
                                self.log_connection_details(conn, proc.name())
                                
                        except (psutil.NoSuchProcess, psutil.AccessDenied):
                            continue
                
                time.sleep(1)
                
        except Exception as e:
            logger.error(f"Error monitoring port {port}: {e}")
    
    def log_connection_details(self, conn, process_name: str):
        """Log detailed connection information"""
        timestamp = datetime.now().isoformat()
        
        connection_detail = {
            'timestamp': timestamp,
            'process_name': process_name,
            'local_address': f"{conn.laddr.ip}:{conn.laddr.port}" if conn.laddr else "N/A",
            'remote_address': f"{conn.raddr.ip}:{conn.raddr.port}" if conn.raddr else "N/A",
            'status': conn.status,
            'protocol': 'TCP' if conn.type == socket.SOCK_STREAM else 'UDP'
        }
        
        self.captured_data.append(connection_detail)
        logger.info(f"Connection Detail: {connection_detail}")
    
    def start_interception(self):
        """Start all interception methods"""
        logger.info("Starting Z3X Samstool network interception...")
        self.running = True
        
        # Start packet capture
        self.start_packet_capture()
        
        # Start process monitoring
        threading.Thread(target=self.monitor_processes, daemon=True).start()
        
        # Setup proxy/socket monitoring
        self.setup_proxy_server()
        
        logger.info("All interception methods started. Press Ctrl+C to stop.")
        
        try:
            while self.running:
                time.sleep(1)
        except KeyboardInterrupt:
            self.stop_interception()
    
    def stop_interception(self):
        """Stop all interception"""
        logger.info("Stopping network interception...")
        self.running = False
        self.stop_packet_capture()
        
        # Save captured data
        self.save_captured_data()
        logger.info("Interception stopped.")
    
    def save_captured_data(self):
        """Save all captured data to file"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"z3x_captured_data_{timestamp}.json"
        
        try:
            with open(filename, 'w') as f:
                json.dump(self.captured_data, f, indent=2)
            
            logger.info(f"Captured data saved to {filename}")
            logger.info(f"Total captured entries: {len(self.captured_data)}")
            
        except Exception as e:
            logger.error(f"Error saving captured data: {e}")

if __name__ == "__main__":
    interceptor = NetworkInterceptor()
    
    print("Z3X Samstool Network Interceptor")
    print("================================")
    print("This tool will monitor all network communications from Z3X Samstool")
    print("and identify how it fingerprints your PC.")
    print("\nPress Enter to start interception...")
    input()
    
    try:
        interceptor.start_interception()
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        print(f"Error: {e}")
