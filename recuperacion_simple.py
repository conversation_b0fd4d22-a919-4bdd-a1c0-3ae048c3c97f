#!/usr/bin/env python3
"""
RECUPERACIÓN SIMPLE Z3X - Método alternativo sin privilegios de administrador
"""

import os
import sys
import json
import shutil
import random
import string
from datetime import datetime

def create_backup():
    """Crea backup de archivos de usuario"""
    backup_dir = f"BACKUP_SIMPLE_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    os.makedirs(backup_dir, exist_ok=True)
    
    print(f"💾 Creando backup en: {backup_dir}")
    
    # Backup de archivos Z3X de usuario
    try:
        z3x_appdata = os.path.expanduser(r"~\AppData\Roaming\z3x")
        if os.path.exists(z3x_appdata):
            shutil.copytree(z3x_appdata, f"{backup_dir}\\z3x_appdata_original", dirs_exist_ok=True)
            print("✅ Backup de archivos Z3X creado")
    except Exception as e:
        print(f"❌ Error en backup: {e}")
    
    return backup_dir

def clean_user_z3x_files():
    """Limpia archivos Z3X del usuario"""
    try:
        print("🧹 Limpiando archivos Z3X de usuario...")
        
        # Eliminar configuración de usuario
        z3x_appdata = os.path.expanduser(r"~\AppData\Roaming\z3x")
        if os.path.exists(z3x_appdata):
            shutil.rmtree(z3x_appdata)
            print(f"✅ Eliminado: {z3x_appdata}")
        
        # Limpiar archivos temporales del usuario
        user_temp = os.path.expanduser(r"~\AppData\Local\Temp")
        if os.path.exists(user_temp):
            for root, dirs, files in os.walk(user_temp):
                for file in files:
                    if any(term in file.lower() for term in ['z3x', 'samstool']):
                        try:
                            file_path = os.path.join(root, file)
                            os.remove(file_path)
                            print(f"✅ Eliminado: {file}")
                        except:
                            pass
        
        # Limpiar Downloads
        downloads = os.path.expanduser(r"~\Downloads")
        if os.path.exists(downloads):
            for file in os.listdir(downloads):
                if any(term in file.lower() for term in ['z3x', 'samstool']):
                    try:
                        file_path = os.path.join(downloads, file)
                        if os.path.isfile(file_path):
                            os.remove(file_path)
                            print(f"✅ Eliminado de Downloads: {file}")
                    except:
                        pass
        
        return True
    except Exception as e:
        print(f"❌ Error limpiando archivos: {e}")
        return False

def create_new_z3x_config():
    """Crea nueva configuración Z3X con datos limpios"""
    try:
        print("🔧 Creando nueva configuración Z3X...")
        
        # Crear directorio Z3X limpio
        z3x_dir = os.path.expanduser(r"~\AppData\Roaming\z3x")
        os.makedirs(z3x_dir, exist_ok=True)
        
        # Crear shared.ini con configuración básica
        shared_ini = os.path.join(z3x_dir, "shared.ini")
        with open(shared_ini, "w", encoding="utf-8") as f:
            f.write("[s]\n")
            f.write(f"downloadFolder={os.path.expanduser('~\\Downloads')}\n")
        
        print(f"✅ Nueva configuración creada: {shared_ini}")
        return True
    except Exception as e:
        print(f"❌ Error creando configuración: {e}")
        return False

def generate_mac_spoof_instructions():
    """Genera instrucciones para cambiar MAC manualmente"""
    instructions = f"""
# INSTRUCCIONES PARA CAMBIAR MAC ADDRESS MANUALMENTE

## Método 1: Administrador de dispositivos
1. Presiona Win + X y selecciona "Administrador de dispositivos"
2. Expande "Adaptadores de red"
3. Haz clic derecho en tu adaptador principal
4. Selecciona "Propiedades"
5. Ve a la pestaña "Avanzado"
6. Busca "Network Address" o "Locally Administered Address"
7. Selecciona "Valor" e ingresa: {generate_new_mac().replace(':', '')}
8. Haz clic en "Aceptar"
9. Reinicia el adaptador de red

## Método 2: Usando netsh (como Administrador)
1. Abre CMD como Administrador
2. Ejecuta: netsh interface show interface
3. Anota el nombre de tu adaptador
4. Ejecuta: netsh interface set interface "NOMBRE_ADAPTADOR" admin=disable
5. Cambia MAC en el registro (ver Método 1)
6. Ejecuta: netsh interface set interface "NOMBRE_ADAPTADOR" admin=enable

## Método 3: Herramientas de terceros
1. Descargar "Technitium MAC Address Changer"
2. Instalar y ejecutar como Administrador
3. Seleccionar adaptador
4. Generar nueva MAC aleatoria
5. Aplicar cambios
6. Reiniciar adaptador

## Nueva MAC sugerida: {generate_new_mac()}

IMPORTANTE: Después de cambiar la MAC, reinicia completamente el sistema.
"""
    
    with open("CAMBIAR_MAC_MANUAL.txt", "w", encoding="utf-8") as f:
        f.write(instructions)
    
    print("📋 Instrucciones de MAC creadas: CAMBIAR_MAC_MANUAL.txt")

def generate_new_mac():
    """Genera nueva MAC address"""
    prefixes = ["00:1B:44", "00:50:B6", "00:E0:4C", "08:00:27", "52:54:00"]
    prefix = random.choice(prefixes)
    suffix = ":".join([f"{random.randint(0, 255):02X}" for _ in range(3)])
    return f"{prefix}:{suffix}"

def create_vm_detailed_guide():
    """Crea guía detallada para máquina virtual"""
    guide = """
# GUÍA COMPLETA PARA MÁQUINA VIRTUAL Z3X

## ¿Por qué usar una máquina virtual?
- Hardware completamente diferente al sistema principal
- Z3X verá la VM como un PC totalmente distinto
- Fácil de resetear si hay problemas
- Aislamiento total del sistema principal

## OPCIÓN 1: VirtualBox (GRATIS)

### Paso 1: Descargar e instalar
1. Ve a: https://www.virtualbox.org/
2. Descarga VirtualBox para Windows
3. Instala con configuración por defecto

### Paso 2: Crear máquina virtual
1. Abre VirtualBox
2. Clic en "Nueva"
3. Nombre: "Z3X-Recovery"
4. Tipo: Microsoft Windows
5. Versión: Windows 10 (64-bit)
6. RAM: 4096 MB (4GB)
7. Crear disco duro virtual: 50GB
8. Tipo de archivo: VDI
9. Almacenamiento: Asignado dinámicamente

### Paso 3: Configurar hardware único
1. Selecciona la VM creada
2. Clic en "Configuración"
3. Sistema → Procesador: 2 CPUs
4. Red → Adaptador 1: NAT
5. Avanzado → Generar nueva MAC

### Paso 4: Instalar Windows
1. Descargar ISO de Windows 10/11
2. En VirtualBox: Configuración → Almacenamiento
3. Agregar ISO como unidad óptica
4. Iniciar VM e instalar Windows

### Paso 5: Instalar Z3X
1. Dentro de la VM, descargar Z3X Samstool
2. Instalar normalmente
3. Usar tu cuenta Z3X

## OPCIÓN 2: VMware Workstation (MEJOR RENDIMIENTO)

### Paso 1: Descargar VMware
1. Ve a: https://www.vmware.com/products/workstation-pro.html
2. Descarga versión de prueba
3. Instala con configuración por defecto

### Paso 2: Crear VM
1. Abre VMware Workstation
2. File → New Virtual Machine
3. Typical configuration
4. Installer disc image: Seleccionar ISO de Windows
5. Guest OS: Windows 10 x64
6. VM Name: "Z3X-Recovery"
7. Disk size: 50GB
8. RAM: 4GB

### Paso 3: Configurar hardware
1. Edit virtual machine settings
2. Network Adapter: NAT
3. Advanced → Generate new MAC
4. Processors: 2 cores

## VENTAJAS DE CADA MÉTODO

### VirtualBox:
✅ Completamente gratis
✅ Fácil de usar
✅ Buena compatibilidad
❌ Rendimiento menor

### VMware:
✅ Mejor rendimiento
✅ Más características
✅ Mejor soporte 3D
❌ Costo (versión completa)

## CONFIGURACIÓN RECOMENDADA PARA Z3X

### Especificaciones mínimas:
- RAM: 4GB
- Disco: 50GB
- CPU: 2 cores
- Red: NAT
- 3D: Habilitado

### Configuración de red:
- Tipo: NAT (recomendado)
- Alternativa: Bridge (si NAT no funciona)

## PASOS DESPUÉS DE CREAR LA VM

1. **Instalar Windows completamente**
2. **Activar Windows** (usar clave legítima)
3. **Instalar VMware Tools** o **VirtualBox Guest Additions**
4. **Configurar red** y verificar conectividad
5. **Descargar Z3X Samstool** dentro de la VM
6. **Instalar Z3X** normalmente
7. **Intentar login** con tu cuenta

## SOLUCIÓN DE PROBLEMAS

### Si Z3X no funciona en VM:
1. Verificar que la virtualización esté habilitada
2. Asignar más RAM (6-8GB)
3. Habilitar aceleración 3D
4. Cambiar tipo de red (NAT ↔ Bridge)
5. Generar nueva MAC address

### Si hay problemas de rendimiento:
1. Aumentar RAM asignada
2. Aumentar CPUs virtuales
3. Habilitar aceleración de hardware
4. Cerrar programas innecesarios en host

## BACKUP Y SNAPSHOTS

### Crear snapshot antes de usar Z3X:
1. VM → Snapshot → Take Snapshot
2. Nombre: "Windows-Limpio"
3. Descripción: "Antes de instalar Z3X"

### Si algo sale mal:
1. VM → Snapshot → Revert to Snapshot
2. Seleccionar "Windows-Limpio"
3. Intentar nuevamente

## CONSIDERACIONES IMPORTANTES

⚠️ **Licencias**: Necesitas licencia válida de Windows para la VM
⚠️ **Rendimiento**: La VM será más lenta que el sistema nativo
⚠️ **Recursos**: Asegúrate de tener suficiente RAM y espacio en disco
⚠️ **Drivers**: Instala Guest Additions/Tools para mejor rendimiento

## ALTERNATIVAS SI LA VM NO FUNCIONA

1. **Usar otro PC físico** con hardware diferente
2. **Cambiar hardware físico** (nueva tarjeta de red, etc.)
3. **Contactar soporte Z3X** con nueva información de hardware
4. **Usar herramientas de spoofing** más avanzadas

¡La máquina virtual es la opción MÁS SEGURA y EFECTIVA!
"""
    
    with open("GUIA_COMPLETA_VM.txt", "w", encoding="utf-8") as f:
        f.write(guide)
    
    print("📖 Guía completa de VM creada: GUIA_COMPLETA_VM.txt")

def create_contact_support_info():
    """Crea información para contactar soporte Z3X"""
    support_info = f"""
# INFORMACIÓN PARA CONTACTAR SOPORTE Z3X

## Situación a reportar:
"Mi cuenta fue comprometida por un tercero y ahora no puedo acceder desde mi PC legítimo. He limpiado mi sistema y necesito reautorizar mi hardware."

## Información de hardware actual:
- Machine GUID: 42006B9C-19E1-0000-0000-000000000000
- CPU: AMD64 Family 25 Model 80 Stepping 0
- Disk Serial: 0000_0006_2212_1378_CAF2_5B04_1000_1713
- System UUID: 42006B9C-19E1-0000-0000-000000000000

## Nueva MAC sugerida: {generate_new_mac()}

## Mensaje para soporte:
"Hola, mi cuenta Z3X fue comprometida y alguien más la está usando. He limpiado completamente mi sistema y cambiado identificadores de hardware por seguridad. Necesito reautorizar mi PC legítimo con los nuevos identificadores de hardware. Tengo prueba de compra original y puedo verificar mi identidad."

## Información a proporcionar:
1. Número de serie de tu licencia Z3X
2. Prueba de compra original
3. Nueva información de hardware (después de cambios)
4. Explicación de la situación de seguridad

## Canales de contacto Z3X:
- Sitio web oficial: https://z3x-team.com/
- Foro de soporte: Buscar en su sitio oficial
- Email de soporte: Verificar en su sitio web

## Documentos a preparar:
- Captura de pantalla del error de login
- Prueba de compra de la licencia
- Nueva información de hardware
- Explicación detallada del problema

IMPORTANTE: Sé honesto sobre la situación de seguridad. El soporte técnico puede ayudarte mejor si entienden que tu cuenta fue comprometida.
"""
    
    with open("CONTACTAR_SOPORTE_Z3X.txt", "w", encoding="utf-8") as f:
        f.write(support_info)
    
    print("📞 Información de soporte creada: CONTACTAR_SOPORTE_Z3X.txt")

def generate_recovery_report(backup_dir, actions):
    """Genera reporte de recuperación simple"""
    report = {
        "timestamp": datetime.now().isoformat(),
        "method": "Recuperación Simple (sin privilegios de administrador)",
        "backup_directory": backup_dir,
        "actions_performed": actions,
        "original_hardware": {
            "machine_guid": "42006B9C-19E1-0000-0000-000000000000",
            "cpu_id": "AMD64 Family 25 Model 80 Stepping 0",
            "disk_serial": "0000_0006_2212_1378_CAF2_5B04_1000_1713",
            "system_uuid": "42006B9C-19E1-0000-0000-000000000000"
        },
        "next_steps": [
            "1. Cambiar MAC address manualmente (ver CAMBIAR_MAC_MANUAL.txt)",
            "2. Reiniciar el sistema completamente",
            "3. Intentar acceder a Z3X Samstool",
            "4. Si no funciona, usar máquina virtual (ver GUIA_COMPLETA_VM.txt)",
            "5. Contactar soporte Z3X (ver CONTACTAR_SOPORTE_Z3X.txt)"
        ],
        "files_created": [
            "CAMBIAR_MAC_MANUAL.txt",
            "GUIA_COMPLETA_VM.txt", 
            "CONTACTAR_SOPORTE_Z3X.txt"
        ]
    }
    
    report_file = f"REPORTE_SIMPLE_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(report_file, "w", encoding="utf-8") as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print(f"📊 Reporte guardado: {report_file}")
    return report_file

def main():
    print("="*70)
    print("🔓 RECUPERACIÓN SIMPLE DE CUENTA Z3X")
    print("="*70)
    print("Este método NO requiere privilegios de administrador.")
    print("Limpiará archivos de usuario y creará guías detalladas")
    print("para completar la recuperación manualmente.")
    print("="*70)
    
    # Crear backup
    backup_dir = create_backup()
    
    actions = []
    
    print(f"\n{'='*50}")
    print("🧹 LIMPIANDO ARCHIVOS Z3X DE USUARIO")
    print("="*50)
    
    # Limpiar archivos de usuario
    if clean_user_z3x_files():
        actions.append("Archivos Z3X de usuario eliminados")
        print("✅ Archivos Z3X de usuario limpiados")
    
    # Crear nueva configuración
    if create_new_z3x_config():
        actions.append("Nueva configuración Z3X creada")
        print("✅ Nueva configuración Z3X creada")
    
    print(f"\n{'='*50}")
    print("📋 CREANDO GUÍAS DE RECUPERACIÓN")
    print("="*50)
    
    # Crear guías
    generate_mac_spoof_instructions()
    actions.append("Instrucciones de MAC creadas")
    
    create_vm_detailed_guide()
    actions.append("Guía completa de VM creada")
    
    create_contact_support_info()
    actions.append("Información de soporte creada")
    
    # Generar reporte
    print(f"\n{'='*50}")
    print("📊 GENERANDO REPORTE")
    print("="*50)
    
    report_file = generate_recovery_report(backup_dir, actions)
    
    # Mostrar resumen final
    print(f"\n{'='*70}")
    print("✅ RECUPERACIÓN SIMPLE COMPLETADA")
    print("="*70)
    
    print(f"📁 Backup creado: {backup_dir}")
    print(f"📊 Reporte: {report_file}")
    
    print(f"\n📋 ARCHIVOS CREADOS:")
    print("  📄 CAMBIAR_MAC_MANUAL.txt - Instrucciones para cambiar MAC")
    print("  📖 GUIA_COMPLETA_VM.txt - Guía detallada de máquina virtual")
    print("  📞 CONTACTAR_SOPORTE_Z3X.txt - Info para contactar soporte")
    
    print(f"\n🚀 PRÓXIMOS PASOS:")
    print("1. 📄 Leer CAMBIAR_MAC_MANUAL.txt y cambiar tu MAC address")
    print("2. 🔄 Reiniciar el sistema completamente")
    print("3. 🔓 Intentar acceder a Z3X Samstool")
    print("4. 💻 Si no funciona, seguir GUIA_COMPLETA_VM.txt")
    print("5. 📞 Si nada funciona, usar CONTACTAR_SOPORTE_Z3X.txt")
    
    print(f"\n💡 RECOMENDACIÓN:")
    print("La opción MÁS EFECTIVA es usar una máquina virtual.")
    print("Lee GUIA_COMPLETA_VM.txt para instrucciones detalladas.")
    
    print("="*70)
    
    input("\nPresiona Enter para abrir las guías creadas...")
    
    # Abrir archivos creados
    try:
        os.startfile("CAMBIAR_MAC_MANUAL.txt")
        os.startfile("GUIA_COMPLETA_VM.txt")
        os.startfile("CONTACTAR_SOPORTE_Z3X.txt")
    except:
        print("Los archivos están listos para ser abiertos manualmente.")

if __name__ == "__main__":
    main()
