#!/usr/bin/env python3
"""
Authentication Flow Monitor
Monitors and logs the complete authentication process for Z3X Samstool
"""

import socket
import threading
import time
import json
import logging
import re
import base64
import urllib.parse
from datetime import datetime
from typing import Dict, List, Any, Optional
import mitmproxy
from mitmproxy import http, options
from mitmproxy.tools.dump import DumpMaster
import asyncio

logger = logging.getLogger(__name__)

class AuthFlowMonitor:
    def __init__(self):
        self.auth_data = []
        self.running = False
        self.proxy_port = 8889
        self.z3x_domains = [
            'z3x-team.com',
            'samstool.com',
            'z3xteam.com',
            'samsungtool.com'
        ]
        
    def is_z3x_request(self, url: str) -> bool:
        """Check if the request is related to Z3X services"""
        return any(domain in url.lower() for domain in self.z3x_domains)
    
    def extract_credentials(self, content: str) -> Dict[str, Any]:
        """Extract potential credentials from request content"""
        credentials = {}
        
        # Common credential patterns
        patterns = {
            'username': [
                r'username["\']?\s*[:=]\s*["\']?([^"\'&\s]+)',
                r'user["\']?\s*[:=]\s*["\']?([^"\'&\s]+)',
                r'login["\']?\s*[:=]\s*["\']?([^"\'&\s]+)',
                r'email["\']?\s*[:=]\s*["\']?([^"\'&\s]+)'
            ],
            'password': [
                r'password["\']?\s*[:=]\s*["\']?([^"\'&\s]+)',
                r'pass["\']?\s*[:=]\s*["\']?([^"\'&\s]+)',
                r'pwd["\']?\s*[:=]\s*["\']?([^"\'&\s]+)'
            ],
            'token': [
                r'token["\']?\s*[:=]\s*["\']?([^"\'&\s]+)',
                r'auth["\']?\s*[:=]\s*["\']?([^"\'&\s]+)',
                r'bearer["\']?\s*[:=]\s*["\']?([^"\'&\s]+)'
            ],
            'device_id': [
                r'device[_-]?id["\']?\s*[:=]\s*["\']?([^"\'&\s]+)',
                r'machine[_-]?id["\']?\s*[:=]\s*["\']?([^"\'&\s]+)',
                r'hardware[_-]?id["\']?\s*[:=]\s*["\']?([^"\'&\s]+)'
            ],
            'serial': [
                r'serial["\']?\s*[:=]\s*["\']?([^"\'&\s]+)',
                r'hwid["\']?\s*[:=]\s*["\']?([^"\'&\s]+)'
            ]
        }
        
        for cred_type, pattern_list in patterns.items():
            for pattern in pattern_list:
                matches = re.findall(pattern, content, re.IGNORECASE)
                if matches:
                    credentials[cred_type] = matches
        
        return credentials
    
    def extract_device_info(self, content: str) -> Dict[str, Any]:
        """Extract device identification information"""
        device_info = {}
        
        # Device info patterns
        patterns = {
            'mac_address': r'([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})',
            'cpu_id': r'cpu[_-]?id["\']?\s*[:=]\s*["\']?([^"\'&\s]+)',
            'motherboard_serial': r'mb[_-]?serial["\']?\s*[:=]\s*["\']?([^"\'&\s]+)',
            'bios_serial': r'bios[_-]?serial["\']?\s*[:=]\s*["\']?([^"\'&\s]+)',
            'disk_serial': r'disk[_-]?serial["\']?\s*[:=]\s*["\']?([^"\'&\s]+)',
            'windows_id': r'windows[_-]?id["\']?\s*[:=]\s*["\']?([^"\'&\s]+)',
            'machine_guid': r'[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}'
        }
        
        for info_type, pattern in patterns.items():
            matches = re.findall(pattern, content, re.IGNORECASE)
            if matches:
                device_info[info_type] = matches
        
        return device_info
    
    def analyze_headers(self, headers: Dict[str, str]) -> Dict[str, Any]:
        """Analyze HTTP headers for authentication and device info"""
        header_analysis = {
            'auth_headers': {},
            'device_headers': {},
            'suspicious_headers': {}
        }
        
        for header_name, header_value in headers.items():
            header_lower = header_name.lower()
            
            # Authentication headers
            if any(auth_term in header_lower for auth_term in 
                   ['auth', 'token', 'bearer', 'session', 'cookie']):
                header_analysis['auth_headers'][header_name] = header_value
            
            # Device identification headers
            if any(device_term in header_lower for device_term in 
                   ['device', 'machine', 'hardware', 'fingerprint', 'id']):
                header_analysis['device_headers'][header_name] = header_value
            
            # Custom or suspicious headers
            if (header_name.startswith('X-') or 
                any(term in header_lower for term in ['z3x', 'sam', 'tool'])):
                header_analysis['suspicious_headers'][header_name] = header_value
        
        return header_analysis
    
    def log_auth_attempt(self, request_data: Dict[str, Any]):
        """Log authentication attempt with all relevant data"""
        timestamp = datetime.now().isoformat()
        
        auth_entry = {
            'timestamp': timestamp,
            'url': request_data.get('url', ''),
            'method': request_data.get('method', ''),
            'headers': request_data.get('headers', {}),
            'body': request_data.get('body', ''),
            'credentials': self.extract_credentials(request_data.get('body', '')),
            'device_info': self.extract_device_info(request_data.get('body', '')),
            'header_analysis': self.analyze_headers(request_data.get('headers', {})),
            'response_status': request_data.get('response_status'),
            'response_headers': request_data.get('response_headers', {}),
            'response_body': request_data.get('response_body', '')
        }
        
        self.auth_data.append(auth_entry)
        
        # Log important findings
        if auth_entry['credentials']:
            logger.warning(f"Credentials detected in request to {auth_entry['url']}")
            
        if auth_entry['device_info']:
            logger.warning(f"Device info detected in request to {auth_entry['url']}")
            
        if auth_entry['header_analysis']['suspicious_headers']:
            logger.warning(f"Suspicious headers detected: {auth_entry['header_analysis']['suspicious_headers']}")
    
    def setup_mitm_proxy(self):
        """Setup mitmproxy to intercept HTTPS traffic"""
        try:
            opts = options.Options(listen_port=self.proxy_port)
            master = DumpMaster(opts)
            
            def request(flow: http.HTTPFlow) -> None:
                """Handle intercepted requests"""
                if self.is_z3x_request(flow.request.pretty_url):
                    request_data = {
                        'url': flow.request.pretty_url,
                        'method': flow.request.method,
                        'headers': dict(flow.request.headers),
                        'body': flow.request.text or ''
                    }
                    
                    logger.info(f"Intercepted Z3X request: {request_data['method']} {request_data['url']}")
                    
                    # Store for later analysis when response arrives
                    flow.request.z3x_data = request_data
            
            def response(flow: http.HTTPFlow) -> None:
                """Handle intercepted responses"""
                if hasattr(flow.request, 'z3x_data'):
                    request_data = flow.request.z3x_data
                    request_data.update({
                        'response_status': flow.response.status_code,
                        'response_headers': dict(flow.response.headers),
                        'response_body': flow.response.text or ''
                    })
                    
                    self.log_auth_attempt(request_data)
            
            master.addons.add(request)
            master.addons.add(response)
            
            logger.info(f"MITM proxy started on port {self.proxy_port}")
            
            # Run the proxy
            asyncio.run(master.run())
            
        except Exception as e:
            logger.error(f"Error setting up MITM proxy: {e}")
            return False
    
    def monitor_registry_access(self):
        """Monitor registry access for authentication-related keys"""
        # This would require additional tools like Process Monitor or WinAPIOverride
        # For now, we'll implement a basic monitoring approach
        
        logger.info("Registry monitoring would require additional tools")
        logger.info("Consider using Process Monitor (ProcMon) to track registry access")
    
    def monitor_file_access(self):
        """Monitor file system access for license/auth files"""
        import os
        import glob
        
        # Common locations for license/auth files
        search_paths = [
            os.path.expanduser("~"),
            "C:\\Program Files",
            "C:\\Program Files (x86)",
            "C:\\ProgramData",
            os.path.join(os.path.expanduser("~"), "AppData", "Local"),
            os.path.join(os.path.expanduser("~"), "AppData", "Roaming")
        ]
        
        # Common file patterns
        patterns = [
            "*z3x*",
            "*sam*tool*",
            "*.lic",
            "*.license",
            "*.key",
            "*auth*",
            "*hwid*"
        ]
        
        found_files = []
        
        for search_path in search_paths:
            if os.path.exists(search_path):
                for pattern in patterns:
                    try:
                        files = glob.glob(os.path.join(search_path, "**", pattern), 
                                        recursive=True)
                        found_files.extend(files)
                    except Exception as e:
                        continue
        
        if found_files:
            logger.info(f"Found potential Z3X-related files: {found_files}")
            
        return found_files
    
    def start_monitoring(self):
        """Start all authentication monitoring"""
        logger.info("Starting Z3X authentication flow monitoring...")
        self.running = True
        
        # Start file monitoring
        threading.Thread(target=self.monitor_file_access, daemon=True).start()
        
        # Start registry monitoring
        threading.Thread(target=self.monitor_registry_access, daemon=True).start()
        
        # Setup MITM proxy (this will block)
        try:
            self.setup_mitm_proxy()
        except KeyboardInterrupt:
            self.stop_monitoring()
    
    def stop_monitoring(self):
        """Stop monitoring and save data"""
        logger.info("Stopping authentication monitoring...")
        self.running = False
        self.save_auth_data()
    
    def save_auth_data(self):
        """Save captured authentication data"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"z3x_auth_data_{timestamp}.json"
        
        try:
            with open(filename, 'w') as f:
                json.dump(self.auth_data, f, indent=2)
            
            logger.info(f"Authentication data saved to {filename}")
            logger.info(f"Total auth attempts captured: {len(self.auth_data)}")
            
        except Exception as e:
            logger.error(f"Error saving auth data: {e}")

if __name__ == "__main__":
    monitor = AuthFlowMonitor()
    
    print("Z3X Authentication Flow Monitor")
    print("===============================")
    print(f"This will intercept HTTPS traffic on port {monitor.proxy_port}")
    print("Configure Z3X Samstool to use this proxy:")
    print(f"  HTTP Proxy: 127.0.0.1:{monitor.proxy_port}")
    print("\nPress Enter to start monitoring...")
    input()
    
    try:
        monitor.start_monitoring()
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        print(f"Error: {e}")
