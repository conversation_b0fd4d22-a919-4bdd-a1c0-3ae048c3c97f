@echo off
echo ================================================================
echo 🔓 RECUPERACION AUTOMATICA DE CUENTA Z3X SAMSTOOL
echo ================================================================
echo Esta herramienta recuperara automaticamente tu cuenta Z3X
echo que fue robada, modificando los identificadores de hardware.
echo ================================================================

:: Verificar privilegios de administrador
net session >nul 2>&1
if %errorLevel% == 0 (
    echo [OK] Ejecutandose como Administrador
    goto :start_recovery
) else (
    echo [INFO] Solicitando privilegios de Administrador...
    
    :: Crear script temporal para ejecutar con privilegios
    echo python "%~dp0recuperar_cuenta_z3x.py" > "%temp%\run_recovery.bat"
    
    :: Ejecutar como administrador
    powershell -Command "Start-Process cmd -ArgumentList '/c %temp%\run_recovery.bat & pause' -Verb RunAs"
    
    echo [INFO] Se abrio una nueva ventana como Administrador
    echo [INFO] Continua el proceso en esa ventana
    pause
    exit /b 0
)

:start_recovery
echo.
echo ================================================================
echo 🚀 INICIANDO RECUPERACION AUTOMATICA
echo ================================================================

:: Cambiar al directorio del script
cd /d "%~dp0"

:: Ejecutar recuperacion
python recuperar_cuenta_z3x.py

echo.
echo ================================================================
echo ✅ PROCESO COMPLETADO
echo ================================================================
echo Tu cuenta Z3X deberia estar recuperada.
echo Reinicia el sistema e intenta acceder a Z3X Samstool.
echo ================================================================
pause
