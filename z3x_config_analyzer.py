#!/usr/bin/env python3
"""
Z3X Configuration Analyzer - Analiza archivos de configuración y licencias de Z3X
"""

import os
import json
import re
import base64
import binascii
import struct
from datetime import datetime
import logging
import configparser

logger = logging.getLogger(__name__)

class Z3XConfigAnalyzer:
    def __init__(self):
        self.hardware_identifiers = {}
        self.license_info = {}
        self.config_data = {}
        self.encrypted_data = {}
        
    def analyze_ini_file(self, file_path):
        """Analiza archivos .ini de configuración"""
        try:
            config = configparser.ConfigParser()
            config.read(file_path, encoding='utf-8')
            
            config_data = {}
            
            for section in config.sections():
                config_data[section] = {}
                for key, value in config.items(section):
                    config_data[section][key] = value
                    
                    # Buscar identificadores de hardware
                    if any(hw_term in key.lower() for hw_term in 
                           ['hwid', 'hardware', 'serial', 'mac', 'guid', 'cpu', 'disk']):
                        self.hardware_identifiers[f"{section}.{key}"] = value
                        print(f"🔍 Hardware ID encontrado: {key} = {value}")
            
            return config_data
            
        except Exception as e:
            logger.error(f"Error analizando INI {file_path}: {e}")
            return {}
    
    def analyze_license_file(self, file_path):
        """Analiza archivos de licencia"""
        try:
            with open(file_path, 'rb') as f:
                data = f.read()
            
            license_info = {
                'file_path': file_path,
                'file_size': len(data),
                'raw_data': data.hex()[:200],  # Primeros 100 bytes en hex
                'is_binary': not all(b < 128 for b in data[:100]),
                'possible_formats': []
            }
            
            # Intentar diferentes formatos
            try:
                # Intentar como texto
                text_data = data.decode('utf-8', errors='ignore')
                if len(text_data.strip()) > 0:
                    license_info['text_content'] = text_data
                    license_info['possible_formats'].append('text')
                    
                    # Buscar patrones en texto
                    self.extract_text_patterns(text_data, license_info)
                    
            except:
                pass
            
            # Intentar como base64
            try:
                decoded = base64.b64decode(data)
                license_info['base64_decoded'] = decoded.hex()[:200]
                license_info['possible_formats'].append('base64')
            except:
                pass
            
            # Buscar patrones binarios
            self.analyze_binary_patterns(data, license_info)
            
            return license_info
            
        except Exception as e:
            logger.error(f"Error analizando licencia {file_path}: {e}")
            return {}
    
    def extract_text_patterns(self, text, license_info):
        """Extrae patrones relevantes del texto"""
        patterns = {
            'mac_address': r'([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})',
            'guid': r'[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}',
            'serial_number': r'[A-Z0-9]{8,20}',
            'hwid': r'HWID[:\s]*([A-Z0-9]+)',
            'cpu_id': r'CPU[_\s]*ID[:\s]*([A-Z0-9]+)',
            'email': r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}',
            'username': r'[Uu]ser[:\s]*([A-Za-z0-9_]+)',
            'license_key': r'[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}'
        }
        
        found_patterns = {}
        
        for pattern_name, pattern in patterns.items():
            matches = re.findall(pattern, text, re.IGNORECASE)
            if matches:
                found_patterns[pattern_name] = matches
                print(f"🔑 Patrón encontrado ({pattern_name}): {matches}")
        
        license_info['extracted_patterns'] = found_patterns
    
    def analyze_binary_patterns(self, data, license_info):
        """Analiza patrones en datos binarios"""
        binary_info = {}
        
        # Buscar strings ASCII en binario
        ascii_strings = []
        current_string = ""
        
        for byte in data:
            if 32 <= byte <= 126:  # Caracteres ASCII imprimibles
                current_string += chr(byte)
            else:
                if len(current_string) >= 4:
                    ascii_strings.append(current_string)
                current_string = ""
        
        if len(current_string) >= 4:
            ascii_strings.append(current_string)
        
        binary_info['ascii_strings'] = ascii_strings[:20]  # Primeros 20 strings
        
        # Buscar patrones de hardware en strings
        for string in ascii_strings:
            if any(hw_term in string.lower() for hw_term in 
                   ['hwid', 'serial', 'mac', 'cpu', 'disk', 'guid']):
                print(f"🔍 String de hardware en binario: {string}")
        
        license_info['binary_analysis'] = binary_info
    
    def analyze_log_file(self, file_path):
        """Analiza archivos de log"""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            log_info = {
                'file_path': file_path,
                'line_count': len(content.split('\n')),
                'hardware_references': [],
                'network_activity': [],
                'authentication_attempts': []
            }
            
            lines = content.split('\n')
            
            for line_num, line in enumerate(lines, 1):
                line_lower = line.lower()
                
                # Buscar referencias de hardware
                if any(hw_term in line_lower for hw_term in 
                       ['hwid', 'hardware', 'serial', 'mac', 'cpu', 'disk']):
                    log_info['hardware_references'].append({
                        'line': line_num,
                        'content': line.strip()
                    })
                
                # Buscar actividad de red
                if any(net_term in line_lower for net_term in 
                       ['connect', 'server', 'http', 'login', 'auth']):
                    log_info['network_activity'].append({
                        'line': line_num,
                        'content': line.strip()
                    })
                
                # Buscar intentos de autenticación
                if any(auth_term in line_lower for auth_term in 
                       ['login', 'password', 'user', 'auth', 'credential']):
                    log_info['authentication_attempts'].append({
                        'line': line_num,
                        'content': line.strip()
                    })
            
            return log_info
            
        except Exception as e:
            logger.error(f"Error analizando log {file_path}: {e}")
            return {}
    
    def analyze_data_file(self, file_path):
        """Analiza archivos .dat y otros archivos de datos"""
        try:
            with open(file_path, 'rb') as f:
                data = f.read()
            
            data_info = {
                'file_path': file_path,
                'file_size': len(data),
                'entropy': self.calculate_entropy(data),
                'is_encrypted': False,
                'possible_encryption': []
            }
            
            # Calcular entropía para detectar encriptación
            if data_info['entropy'] > 7.5:
                data_info['is_encrypted'] = True
                data_info['possible_encryption'].append('high_entropy')
                print(f"🔒 Archivo posiblemente encriptado: {file_path}")
            
            # Buscar headers conocidos
            header = data[:16]
            if header.startswith(b'PK'):
                data_info['possible_encryption'].append('zip_archive')
            elif header.startswith(b'\x7fELF'):
                data_info['possible_encryption'].append('elf_binary')
            elif header.startswith(b'MZ'):
                data_info['possible_encryption'].append('pe_executable')
            
            # Intentar extraer strings
            strings = self.extract_strings_from_binary(data)
            data_info['extracted_strings'] = strings[:50]  # Primeros 50 strings
            
            return data_info
            
        except Exception as e:
            logger.error(f"Error analizando datos {file_path}: {e}")
            return {}
    
    def calculate_entropy(self, data):
        """Calcula la entropía de los datos"""
        if not data:
            return 0
        
        import math
        from collections import Counter
        
        counts = Counter(data)
        entropy = 0
        
        for count in counts.values():
            p = count / len(data)
            entropy -= p * math.log2(p)
        
        return entropy
    
    def extract_strings_from_binary(self, data):
        """Extrae strings de datos binarios"""
        strings = []
        current_string = ""
        
        for byte in data:
            if 32 <= byte <= 126:  # ASCII imprimible
                current_string += chr(byte)
            else:
                if len(current_string) >= 4:
                    strings.append(current_string)
                current_string = ""
        
        if len(current_string) >= 4:
            strings.append(current_string)
        
        return strings
    
    def analyze_all_files(self, scan_report_file):
        """Analiza todos los archivos encontrados en el escaneo"""
        try:
            with open(scan_report_file, 'r', encoding='utf-8') as f:
                scan_data = json.load(f)
            
            print("="*60)
            print("ANALIZANDO ARCHIVOS DE CONFIGURACIÓN Z3X")
            print("="*60)
            
            analysis_results = {
                'timestamp': datetime.now().isoformat(),
                'config_analysis': {},
                'license_analysis': {},
                'log_analysis': {},
                'data_analysis': {},
                'hardware_identifiers': {},
                'summary': {}
            }
            
            # Analizar archivos de configuración
            for config_file in scan_data.get('config_files', []):
                file_path = config_file['path']
                print(f"\n📄 Analizando configuración: {file_path}")
                
                if config_file['extension'] == '.ini':
                    result = self.analyze_ini_file(file_path)
                    analysis_results['config_analysis'][file_path] = result
            
            # Analizar archivos de licencia
            for license_file in scan_data.get('license_files', []):
                file_path = license_file['path']
                print(f"\n🔑 Analizando licencia: {file_path}")
                
                result = self.analyze_license_file(file_path)
                analysis_results['license_analysis'][file_path] = result
            
            # Analizar archivos de log
            for log_file in scan_data.get('log_files', []):
                file_path = log_file['path']
                print(f"\n📋 Analizando log: {file_path}")
                
                result = self.analyze_log_file(file_path)
                analysis_results['log_analysis'][file_path] = result
            
            # Analizar archivos de datos
            for file_info in scan_data.get('found_files', []):
                if file_info['type'] == 'data':
                    file_path = file_info['path']
                    print(f"\n💾 Analizando datos: {file_path}")
                    
                    result = self.analyze_data_file(file_path)
                    analysis_results['data_analysis'][file_path] = result
            
            # Consolidar identificadores de hardware
            analysis_results['hardware_identifiers'] = self.hardware_identifiers
            
            # Generar resumen
            analysis_results['summary'] = {
                'config_files_analyzed': len(analysis_results['config_analysis']),
                'license_files_analyzed': len(analysis_results['license_analysis']),
                'log_files_analyzed': len(analysis_results['log_analysis']),
                'data_files_analyzed': len(analysis_results['data_analysis']),
                'hardware_identifiers_found': len(self.hardware_identifiers)
            }
            
            # Guardar resultados
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            results_file = f"z3x_config_analysis_{timestamp}.json"
            
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump(analysis_results, f, indent=2, ensure_ascii=False)
            
            # Mostrar resumen
            print("\n" + "="*60)
            print("RESUMEN DEL ANÁLISIS")
            print("="*60)
            print(f"Archivos de configuración analizados: {analysis_results['summary']['config_files_analyzed']}")
            print(f"Archivos de licencia analizados: {analysis_results['summary']['license_files_analyzed']}")
            print(f"Archivos de log analizados: {analysis_results['summary']['log_files_analyzed']}")
            print(f"Archivos de datos analizados: {analysis_results['summary']['data_files_analyzed']}")
            print(f"Identificadores de hardware encontrados: {analysis_results['summary']['hardware_identifiers_found']}")
            
            if self.hardware_identifiers:
                print(f"\n🔍 IDENTIFICADORES DE HARDWARE ENCONTRADOS:")
                for key, value in self.hardware_identifiers.items():
                    print(f"  {key}: {value}")
            
            print(f"\nResultados guardados en: {results_file}")
            print("="*60)
            
            return analysis_results
            
        except Exception as e:
            logger.error(f"Error en análisis: {e}")
            return None

if __name__ == "__main__":
    analyzer = Z3XConfigAnalyzer()
    
    print("Z3X Configuration Analyzer")
    print("==========================")
    print("Este analizador examinará los archivos encontrados por el escáner")
    print("para extraer información de identificación de hardware y configuración.")
    
    # Buscar el reporte de escaneo más reciente
    import glob
    scan_reports = glob.glob("z3x_scan_report_*.json")
    
    if not scan_reports:
        print("\n❌ No se encontró ningún reporte de escaneo.")
        print("Ejecuta primero z3x_file_scanner.py")
        exit(1)
    
    latest_report = sorted(scan_reports)[-1]
    print(f"\nUsando reporte de escaneo: {latest_report}")
    print("\nPresiona Enter para comenzar el análisis...")
    input()
    
    try:
        results = analyzer.analyze_all_files(latest_report)
        
        if results:
            print(f"\n✅ Análisis completado exitosamente.")
        else:
            print(f"\n❌ Error durante el análisis.")
            
    except Exception as e:
        print(f"\nError: {e}")
        logger.error(f"Error en análisis: {e}")
