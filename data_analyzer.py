#!/usr/bin/env python3
"""
Data Export and Analysis Module
Analyzes captured data for security vulnerabilities and generates reports
"""

import json
import csv
import xml.etree.ElementTree as ET
from datetime import datetime
import logging
import glob
import os
import hashlib
from typing import Dict, List, Any, Optional
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from collections import Counter, defaultdict

logger = logging.getLogger(__name__)

class DataAnalyzer:
    def __init__(self):
        self.network_data = []
        self.auth_data = []
        self.fingerprint_data = {}
        self.analysis_results = {}
        
    def load_all_data(self):
        """Load all available captured data"""
        logger.info("Loading all captured data for analysis...")
        
        # Load network data
        network_files = glob.glob("z3x_captured_data_*.json")
        for file in network_files:
            try:
                with open(file, 'r') as f:
                    data = json.load(f)
                    self.network_data.extend(data)
            except Exception as e:
                logger.error(f"Error loading {file}: {e}")
        
        # Load auth data
        auth_files = glob.glob("z3x_auth_data_*.json")
        for file in auth_files:
            try:
                with open(file, 'r') as f:
                    data = json.load(f)
                    self.auth_data.extend(data)
            except Exception as e:
                logger.error(f"Error loading {file}: {e}")
        
        # Load fingerprint data
        fingerprint_files = glob.glob("hardware_fingerprint_*.json")
        if fingerprint_files:
            latest_fingerprint = sorted(fingerprint_files, reverse=True)[0]
            try:
                with open(latest_fingerprint, 'r') as f:
                    self.fingerprint_data = json.load(f)
            except Exception as e:
                logger.error(f"Error loading fingerprint data: {e}")
        
        logger.info(f"Loaded {len(self.network_data)} network entries, "
                   f"{len(self.auth_data)} auth entries")
    
    def analyze_network_patterns(self) -> Dict[str, Any]:
        """Analyze network communication patterns"""
        analysis = {
            'unique_remote_ips': set(),
            'communication_frequency': Counter(),
            'port_usage': Counter(),
            'process_activity': Counter(),
            'suspicious_connections': []
        }
        
        for entry in self.network_data:
            # Extract remote IP
            remote_addr = entry.get('remote_address', '')
            if remote_addr and ':' in remote_addr:
                ip, port = remote_addr.split(':')
                analysis['unique_remote_ips'].add(ip)
                analysis['port_usage'][port] += 1
            
            # Count communication frequency
            process_name = entry.get('process_name', 'unknown')
            analysis['process_activity'][process_name] += 1
            
            # Check for suspicious patterns
            if any(suspicious in remote_addr.lower() for suspicious in 
                   ['z3x', 'sam', 'tool', 'crack', 'hack']):
                analysis['suspicious_connections'].append(entry)
        
        # Convert sets to lists for JSON serialization
        analysis['unique_remote_ips'] = list(analysis['unique_remote_ips'])
        
        return analysis
    
    def analyze_authentication_security(self) -> Dict[str, Any]:
        """Analyze authentication security issues"""
        analysis = {
            'credential_exposures': [],
            'device_info_leaks': [],
            'insecure_transmissions': [],
            'authentication_methods': Counter(),
            'security_score': 100  # Start with perfect score, deduct for issues
        }
        
        for entry in self.auth_data:
            url = entry.get('url', '')
            
            # Check for credential exposures
            credentials = entry.get('credentials', {})
            if credentials:
                analysis['credential_exposures'].append({
                    'url': url,
                    'timestamp': entry.get('timestamp'),
                    'credential_types': list(credentials.keys())
                })
                analysis['security_score'] -= 20  # Major security issue
            
            # Check for device info leaks
            device_info = entry.get('device_info', {})
            if device_info:
                analysis['device_info_leaks'].append({
                    'url': url,
                    'timestamp': entry.get('timestamp'),
                    'info_types': list(device_info.keys())
                })
                analysis['security_score'] -= 10
            
            # Check for insecure transmission (HTTP vs HTTPS)
            if url.startswith('http://'):
                analysis['insecure_transmissions'].append({
                    'url': url,
                    'timestamp': entry.get('timestamp'),
                    'issue': 'Unencrypted HTTP transmission'
                })
                analysis['security_score'] -= 15
            
            # Analyze authentication methods
            headers = entry.get('headers', {})
            for header_name in headers:
                if 'auth' in header_name.lower():
                    analysis['authentication_methods'][header_name] += 1
        
        # Ensure score doesn't go below 0
        analysis['security_score'] = max(0, analysis['security_score'])
        
        return analysis
    
    def analyze_fingerprinting_methods(self) -> Dict[str, Any]:
        """Analyze hardware fingerprinting methods used"""
        analysis = {
            'fingerprint_categories': {},
            'unique_identifiers': [],
            'privacy_risk_score': 0,
            'recommendations': []
        }
        
        if not self.fingerprint_data:
            analysis['recommendations'].append("No fingerprint data available for analysis")
            return analysis
        
        # Categorize fingerprint data
        categories = {
            'cpu': ['cpu', 'processor'],
            'memory': ['memory', 'ram'],
            'disk': ['disk', 'drive', 'storage'],
            'network': ['network', 'mac', 'adapter'],
            'system': ['system', 'os', 'windows'],
            'motherboard': ['motherboard', 'board'],
            'bios': ['bios', 'firmware']
        }
        
        for category, keywords in categories.items():
            category_data = {}
            for key, value in self.fingerprint_data.items():
                if any(keyword in key.lower() for keyword in keywords):
                    category_data[key] = value
            
            if category_data:
                analysis['fingerprint_categories'][category] = category_data
                analysis['privacy_risk_score'] += 10
        
        # Identify unique identifiers
        unique_fields = ['serial_number', 'processor_id', 'machine_guid', 'mac_address']
        for field in unique_fields:
            if field in str(self.fingerprint_data):
                analysis['unique_identifiers'].append(field)
                analysis['privacy_risk_score'] += 15
        
        # Generate recommendations
        if analysis['privacy_risk_score'] > 50:
            analysis['recommendations'].append("High privacy risk: Multiple unique identifiers collected")
        if 'mac_address' in analysis['unique_identifiers']:
            analysis['recommendations'].append("MAC address spoofing recommended")
        if 'machine_guid' in analysis['unique_identifiers']:
            analysis['recommendations'].append("Consider changing Windows Machine GUID")
        
        return analysis
    
    def generate_security_report(self) -> Dict[str, Any]:
        """Generate comprehensive security analysis report"""
        logger.info("Generating security analysis report...")
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'summary': {
                'total_network_entries': len(self.network_data),
                'total_auth_entries': len(self.auth_data),
                'fingerprint_available': bool(self.fingerprint_data)
            },
            'network_analysis': self.analyze_network_patterns(),
            'auth_analysis': self.analyze_authentication_security(),
            'fingerprint_analysis': self.analyze_fingerprinting_methods(),
            'recommendations': []
        }
        
        # Generate overall recommendations
        auth_score = report['auth_analysis']['security_score']
        privacy_score = report['fingerprint_analysis']['privacy_risk_score']
        
        if auth_score < 70:
            report['recommendations'].append("CRITICAL: Authentication security issues detected")
        if privacy_score > 60:
            report['recommendations'].append("HIGH: Extensive fingerprinting detected")
        if report['network_analysis']['suspicious_connections']:
            report['recommendations'].append("WARNING: Suspicious network connections found")
        
        # Add mitigation strategies
        report['mitigation_strategies'] = [
            "Use VPN to mask IP address",
            "Spoof MAC addresses",
            "Use virtual machine with different hardware profile",
            "Block Z3X domains at firewall level",
            "Monitor registry changes during Z3X execution",
            "Use process isolation/sandboxing"
        ]
        
        return report
    
    def export_to_csv(self, filename: Optional[str] = None) -> str:
        """Export data to CSV format"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"z3x_analysis_export_{timestamp}.csv"
        
        try:
            # Combine all data for CSV export
            all_data = []
            
            for entry in self.network_data:
                entry['data_type'] = 'network'
                all_data.append(entry)
            
            for entry in self.auth_data:
                entry['data_type'] = 'auth'
                all_data.append(entry)
            
            # Convert to DataFrame and save
            df = pd.DataFrame(all_data)
            df.to_csv(filename, index=False)
            
            logger.info(f"Data exported to CSV: {filename}")
            return filename
            
        except Exception as e:
            logger.error(f"Error exporting to CSV: {e}")
            return ""
    
    def export_to_xml(self, filename: Optional[str] = None) -> str:
        """Export data to XML format"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"z3x_analysis_export_{timestamp}.xml"
        
        try:
            root = ET.Element("z3x_analysis")
            root.set("timestamp", datetime.now().isoformat())
            
            # Add network data
            network_elem = ET.SubElement(root, "network_data")
            for entry in self.network_data:
                entry_elem = ET.SubElement(network_elem, "entry")
                for key, value in entry.items():
                    child = ET.SubElement(entry_elem, key)
                    child.text = str(value)
            
            # Add auth data
            auth_elem = ET.SubElement(root, "auth_data")
            for entry in self.auth_data:
                entry_elem = ET.SubElement(auth_elem, "entry")
                for key, value in entry.items():
                    child = ET.SubElement(entry_elem, key)
                    child.text = str(value)
            
            # Add fingerprint data
            if self.fingerprint_data:
                fp_elem = ET.SubElement(root, "fingerprint_data")
                for key, value in self.fingerprint_data.items():
                    child = ET.SubElement(fp_elem, key)
                    child.text = str(value)
            
            # Write to file
            tree = ET.ElementTree(root)
            tree.write(filename, encoding='utf-8', xml_declaration=True)
            
            logger.info(f"Data exported to XML: {filename}")
            return filename
            
        except Exception as e:
            logger.error(f"Error exporting to XML: {e}")
            return ""
    
    def create_visualizations(self, output_dir: str = "visualizations"):
        """Create data visualizations"""
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        try:
            # Network activity over time
            if self.network_data:
                timestamps = [entry.get('timestamp', '') for entry in self.network_data]
                plt.figure(figsize=(12, 6))
                plt.hist(timestamps, bins=20)
                plt.title('Network Activity Over Time')
                plt.xlabel('Time')
                plt.ylabel('Number of Connections')
                plt.xticks(rotation=45)
                plt.tight_layout()
                plt.savefig(os.path.join(output_dir, 'network_activity.png'))
                plt.close()
            
            # Process activity
            if self.network_data:
                processes = [entry.get('process_name', 'unknown') for entry in self.network_data]
                process_counts = Counter(processes)
                
                plt.figure(figsize=(10, 6))
                plt.bar(process_counts.keys(), process_counts.values())
                plt.title('Network Activity by Process')
                plt.xlabel('Process Name')
                plt.ylabel('Number of Connections')
                plt.xticks(rotation=45)
                plt.tight_layout()
                plt.savefig(os.path.join(output_dir, 'process_activity.png'))
                plt.close()
            
            logger.info(f"Visualizations saved to {output_dir}")
            
        except Exception as e:
            logger.error(f"Error creating visualizations: {e}")
    
    def run_full_analysis(self) -> str:
        """Run complete analysis and generate all outputs"""
        logger.info("Starting full analysis...")
        
        # Load data
        self.load_all_data()
        
        # Generate report
        report = self.generate_security_report()
        
        # Save report
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_filename = f"z3x_security_report_{timestamp}.json"
        
        with open(report_filename, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        # Export data
        csv_file = self.export_to_csv()
        xml_file = self.export_to_xml()
        
        # Create visualizations
        self.create_visualizations()
        
        logger.info(f"Full analysis complete. Report saved to {report_filename}")
        return report_filename

if __name__ == "__main__":
    analyzer = DataAnalyzer()
    
    print("Z3X Data Analyzer")
    print("=================")
    print("Running comprehensive analysis of captured data...")
    
    report_file = analyzer.run_full_analysis()
    
    print(f"\nAnalysis complete!")
    print(f"Security report: {report_file}")
    print("Check the 'visualizations' folder for charts and graphs.")
    
    # Display summary
    with open(report_file, 'r') as f:
        report = json.load(f)
    
    print(f"\nSecurity Summary:")
    print(f"- Authentication Security Score: {report['auth_analysis']['security_score']}/100")
    print(f"- Privacy Risk Score: {report['fingerprint_analysis']['privacy_risk_score']}")
    print(f"- Suspicious Connections: {len(report['network_analysis']['suspicious_connections'])}")
    print(f"- Credential Exposures: {len(report['auth_analysis']['credential_exposures'])}")
    
    if report['recommendations']:
        print(f"\nKey Recommendations:")
        for rec in report['recommendations'][:3]:
            print(f"- {rec}")
