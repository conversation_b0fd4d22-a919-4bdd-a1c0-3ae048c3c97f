#!/usr/bin/env python3
"""
Z3X Recovery Tool - Herramienta principal para recuperar acceso a cuenta Z3X
"""

import os
import sys
import json
import time
import shutil
import logging
from datetime import datetime
import subprocess

# Importar nuestros módulos
from z3x_file_scanner import Z3XFileScanner
from z3x_config_analyzer import Z3XConfigAnalyzer
from z3x_registry_scanner import Z3XRegistryScanner
from hardware_fingerprint_detector import HardwareFingerprintDetector

logger = logging.getLogger(__name__)

class Z3XRecoveryTool:
    def __init__(self):
        self.recovery_data = {}
        self.backup_dir = f"z3x_recovery_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.analysis_complete = False
        
    def check_admin_privileges(self):
        """Verifica privilegios de administrador"""
        try:
            import ctypes
            return ctypes.windll.shell32.IsUserAnAdmin()
        except:
            return False
    
    def create_system_backup(self):
        """Crea backup completo del sistema antes de hacer cambios"""
        print("\n💾 Creando backup del sistema...")
        
        if not os.path.exists(self.backup_dir):
            os.makedirs(self.backup_dir)
        
        # Backup del registro
        try:
            reg_backup = os.path.join(self.backup_dir, "registry_backup.reg")
            cmd = f'reg export HKLM "{reg_backup}"'
            subprocess.run(cmd, shell=True, check=True)
            print(f"✅ Backup del registro creado: {reg_backup}")
        except Exception as e:
            print(f"❌ Error creando backup del registro: {e}")
        
        return self.backup_dir
    
    def run_complete_analysis(self):
        """Ejecuta análisis completo del sistema"""
        print("="*70)
        print("🔍 Z3X RECOVERY TOOL - ANÁLISIS COMPLETO DEL SISTEMA")
        print("="*70)
        print("Esta herramienta analizará completamente tu sistema para")
        print("identificar cómo Z3X Samstool identifica tu PC y te ayudará")
        print("a recuperar el acceso a tu cuenta.")
        print("="*70)
        
        if not self.check_admin_privileges():
            print("\n⚠️  ADVERTENCIA: No se está ejecutando como Administrador!")
            print("Algunas funciones pueden no funcionar correctamente.")
            response = input("¿Continuar de todos modos? (s/n): ")
            if response.lower() != 's':
                return False
        
        # Crear backup del sistema
        backup_dir = self.create_system_backup()
        
        analysis_results = {
            'timestamp': datetime.now().isoformat(),
            'backup_directory': backup_dir,
            'file_scan': None,
            'config_analysis': None,
            'registry_scan': None,
            'hardware_fingerprint': None,
            'recovery_recommendations': []
        }
        
        try:
            # 1. Escaneo de archivos
            print(f"\n{'='*50}")
            print("PASO 1: ESCANEANDO ARCHIVOS DEL SISTEMA")
            print("="*50)
            
            file_scanner = Z3XFileScanner()
            found_files = file_scanner.scan_system()
            analysis_results['file_scan'] = {
                'files_found': len(found_files),
                'scan_report': f"z3x_scan_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            }
            
            # 2. Análisis de configuración
            print(f"\n{'='*50}")
            print("PASO 2: ANALIZANDO ARCHIVOS DE CONFIGURACIÓN")
            print("="*50)
            
            config_analyzer = Z3XConfigAnalyzer()
            # Buscar el reporte más reciente
            import glob
            scan_reports = glob.glob("z3x_scan_report_*.json")
            if scan_reports:
                latest_report = sorted(scan_reports)[-1]
                config_results = config_analyzer.analyze_all_files(latest_report)
                analysis_results['config_analysis'] = config_results
            
            # 3. Escaneo del registro
            print(f"\n{'='*50}")
            print("PASO 3: ESCANEANDO REGISTRO DE WINDOWS")
            print("="*50)
            
            registry_scanner = Z3XRegistryScanner()
            registry_results = registry_scanner.scan_complete_registry()
            analysis_results['registry_scan'] = registry_results
            
            # 4. Fingerprint de hardware
            print(f"\n{'='*50}")
            print("PASO 4: ANALIZANDO FINGERPRINT DE HARDWARE")
            print("="*50)
            
            hw_detector = HardwareFingerprintDetector()
            hw_fingerprint = hw_detector.collect_all_fingerprints()
            hw_detector.save_fingerprint_data()
            analysis_results['hardware_fingerprint'] = hw_fingerprint
            
            # 5. Generar recomendaciones de recuperación
            print(f"\n{'='*50}")
            print("PASO 5: GENERANDO RECOMENDACIONES DE RECUPERACIÓN")
            print("="*50)
            
            recommendations = self.generate_recovery_recommendations(analysis_results)
            analysis_results['recovery_recommendations'] = recommendations
            
            # Guardar análisis completo
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            recovery_file = f"z3x_recovery_analysis_{timestamp}.json"
            
            with open(recovery_file, 'w', encoding='utf-8') as f:
                json.dump(analysis_results, f, indent=2, ensure_ascii=False, default=str)
            
            self.recovery_data = analysis_results
            self.analysis_complete = True
            
            # Mostrar resumen final
            self.show_recovery_summary(analysis_results)
            
            print(f"\n✅ Análisis completo guardado en: {recovery_file}")
            return True
            
        except Exception as e:
            print(f"\n❌ Error durante el análisis: {e}")
            logger.error(f"Error en análisis completo: {e}")
            return False
    
    def generate_recovery_recommendations(self, analysis_results):
        """Genera recomendaciones específicas para recuperar acceso"""
        recommendations = []
        
        # Analizar archivos encontrados
        if analysis_results.get('file_scan', {}).get('files_found', 0) > 0:
            recommendations.append({
                'priority': 'HIGH',
                'category': 'Files',
                'action': 'Backup and modify Z3X configuration files',
                'description': 'Se encontraron archivos de configuración Z3X que pueden contener identificadores de hardware.',
                'steps': [
                    'Hacer backup de todos los archivos Z3X encontrados',
                    'Analizar archivos de licencia para identificar HWID',
                    'Modificar archivos de configuración si es necesario'
                ]
            })
        
        # Analizar hardware fingerprint
        hw_data = analysis_results.get('hardware_fingerprint', {})
        if hw_data:
            critical_ids = []
            
            # Verificar identificadores críticos
            if hw_data.get('registry', {}).get('machine_guid'):
                critical_ids.append('Machine GUID')
            
            cpu_info = hw_data.get('cpu', {})
            if cpu_info.get('processor_id'):
                critical_ids.append('CPU ID')
            
            network_info = hw_data.get('network', {})
            if network_info.get('adapters'):
                mac_addresses = [adapter.get('mac_address') for adapter in network_info['adapters'] 
                               if adapter.get('mac_address')]
                if mac_addresses:
                    critical_ids.append('MAC Addresses')
            
            if critical_ids:
                recommendations.append({
                    'priority': 'CRITICAL',
                    'category': 'Hardware',
                    'action': 'Modify hardware identifiers',
                    'description': f'Se detectaron identificadores críticos: {", ".join(critical_ids)}',
                    'steps': [
                        'Cambiar MAC addresses de adaptadores de red',
                        'Considerar cambiar Machine GUID de Windows',
                        'Usar máquina virtual con hardware diferente',
                        'Modificar identificadores de disco si es posible'
                    ]
                })
        
        # Analizar registro
        registry_data = analysis_results.get('registry_scan', {})
        if registry_data and registry_data.get('summary', {}).get('total_z3x_keys', 0) > 0:
            recommendations.append({
                'priority': 'HIGH',
                'category': 'Registry',
                'action': 'Clean Z3X registry entries',
                'description': 'Se encontraron entradas Z3X en el registro de Windows.',
                'steps': [
                    'Hacer backup completo del registro',
                    'Eliminar entradas Z3X del registro',
                    'Limpiar claves de hardware asociadas',
                    'Reiniciar el sistema después de los cambios'
                ]
            })
        
        # Recomendaciones generales
        recommendations.append({
            'priority': 'MEDIUM',
            'category': 'General',
            'action': 'Use alternative identification methods',
            'description': 'Métodos alternativos para evitar detección de hardware.',
            'steps': [
                'Usar VPN para cambiar IP pública',
                'Ejecutar Z3X en máquina virtual',
                'Usar herramientas de spoofing de hardware',
                'Contactar soporte de Z3X con nueva información de hardware'
            ]
        })
        
        return recommendations
    
    def show_recovery_summary(self, analysis_results):
        """Muestra resumen de recuperación"""
        print("\n" + "="*70)
        print("📋 RESUMEN DE ANÁLISIS Y RECOMENDACIONES DE RECUPERACIÓN")
        print("="*70)
        
        # Mostrar estadísticas
        file_count = analysis_results.get('file_scan', {}).get('files_found', 0)
        registry_keys = analysis_results.get('registry_scan', {}).get('summary', {}).get('total_z3x_keys', 0)
        
        print(f"📁 Archivos Z3X encontrados: {file_count}")
        print(f"🔑 Claves de registro Z3X: {registry_keys}")
        
        # Mostrar identificadores críticos
        hw_data = analysis_results.get('hardware_fingerprint', {})
        if hw_data:
            print(f"\n🔍 IDENTIFICADORES CRÍTICOS DETECTADOS:")
            
            machine_guid = hw_data.get('registry', {}).get('machine_guid')
            if machine_guid:
                print(f"  • Machine GUID: {machine_guid}")
            
            cpu_id = hw_data.get('cpu', {}).get('processor_id')
            if cpu_id:
                print(f"  • CPU ID: {cpu_id}")
            
            # MAC addresses
            network_info = hw_data.get('network', {})
            if network_info.get('adapters'):
                mac_addresses = [adapter.get('mac_address') for adapter in network_info['adapters'] 
                               if adapter.get('mac_address')]
                if mac_addresses:
                    print(f"  • MAC Addresses: {', '.join(mac_addresses[:3])}")
        
        # Mostrar recomendaciones prioritarias
        recommendations = analysis_results.get('recovery_recommendations', [])
        critical_recs = [r for r in recommendations if r.get('priority') == 'CRITICAL']
        high_recs = [r for r in recommendations if r.get('priority') == 'HIGH']
        
        if critical_recs:
            print(f"\n🚨 ACCIONES CRÍTICAS REQUERIDAS:")
            for rec in critical_recs:
                print(f"  • {rec['action']}: {rec['description']}")
        
        if high_recs:
            print(f"\n⚠️  ACCIONES DE ALTA PRIORIDAD:")
            for rec in high_recs:
                print(f"  • {rec['action']}: {rec['description']}")
        
        print("\n" + "="*70)
        print("💡 PRÓXIMOS PASOS RECOMENDADOS:")
        print("="*70)
        print("1. Revisar el archivo de análisis completo generado")
        print("2. Hacer backup de todos los archivos importantes")
        print("3. Implementar las recomendaciones críticas primero")
        print("4. Probar acceso a Z3X después de cada cambio")
        print("5. Contactar soporte de Z3X si es necesario")
        print("="*70)
    
    def execute_recovery_actions(self):
        """Ejecuta acciones de recuperación automáticas"""
        if not self.analysis_complete:
            print("❌ Debe ejecutar el análisis completo primero.")
            return False
        
        print("\n🔧 EJECUTANDO ACCIONES DE RECUPERACIÓN AUTOMÁTICAS")
        print("="*60)
        
        # Implementar acciones automáticas seguras aquí
        # Por ahora, solo mostrar las acciones recomendadas
        
        recommendations = self.recovery_data.get('recovery_recommendations', [])
        
        for i, rec in enumerate(recommendations, 1):
            print(f"\n{i}. {rec['action']} ({rec['priority']})")
            print(f"   {rec['description']}")
            
            if rec['category'] == 'Files':
                # Acciones de archivos
                print("   🔄 Procesando archivos...")
                # Aquí se implementarían las acciones específicas
                
            elif rec['category'] == 'Registry':
                # Acciones de registro
                print("   🔄 Procesando registro...")
                # Aquí se implementarían las acciones específicas
                
            elif rec['category'] == 'Hardware':
                # Acciones de hardware
                print("   🔄 Analizando hardware...")
                # Aquí se implementarían las acciones específicas
        
        print("\n✅ Acciones de recuperación completadas.")
        return True

def main():
    recovery_tool = Z3XRecoveryTool()
    
    print("Z3X Recovery Tool")
    print("=================")
    print("Herramienta completa para recuperar acceso a tu cuenta Z3X Samstool")
    print("analizando cómo identifica tu PC y proporcionando soluciones.")
    
    while True:
        print(f"\n{'='*50}")
        print("OPCIONES DISPONIBLES:")
        print("1. Ejecutar análisis completo del sistema")
        print("2. Ejecutar acciones de recuperación")
        print("3. Mostrar resumen del último análisis")
        print("4. Salir")
        print("="*50)
        
        choice = input("Selecciona una opción (1-4): ").strip()
        
        if choice == '1':
            print("\n⚠️  IMPORTANTE: Este proceso puede tomar 10-30 minutos.")
            print("Se analizará completamente tu sistema buscando información Z3X.")
            confirm = input("¿Continuar? (s/n): ")
            
            if confirm.lower() == 's':
                recovery_tool.run_complete_analysis()
            
        elif choice == '2':
            recovery_tool.execute_recovery_actions()
            
        elif choice == '3':
            if recovery_tool.analysis_complete:
                recovery_tool.show_recovery_summary(recovery_tool.recovery_data)
            else:
                print("❌ No hay análisis disponible. Ejecuta el análisis completo primero.")
        
        elif choice == '4':
            print("👋 Saliendo...")
            break
        
        else:
            print("❌ Opción inválida. Intenta de nuevo.")

if __name__ == "__main__":
    main()
